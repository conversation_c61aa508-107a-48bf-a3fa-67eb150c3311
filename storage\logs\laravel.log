[2025-07-29 16:17:15] local.INFO: Starting executeAction {"job_id":"9f80fa1d-5578-4e5e-b274-ded491ebe520","stage_id":"9f80ec91-59a8-4be6-8c71-b4e61a0b312c","action_id":"9f80ec91-24ed-42fd-b2c4-01f83903d058","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb00489","pending_approval_id":"9f80fa1d-7f7d-4220-b616-7d96b7cd78b8","condition_ids":"[\"9f80ec91-5a5d-4b73-8ff3-b90a638674d1\"]","email_condition_ids":"[]"} 
[2025-07-29 16:17:15] local.WARNING: No data processed in actionProcess {"job_id":"9f80fa1d-5578-4e5e-b274-ded491ebe520","stage_id":"9f80ec91-59a8-4be6-8c71-b4e61a0b312c","action_id":"9f80ec91-24ed-42fd-b2c4-01f83903d058"} 
[2025-07-29 16:17:15] local.ERROR: ActionProcess returned false {"job_id":"9f80fa1d-5578-4e5e-b274-ded491ebe520","stage_id":"9f80ec91-59a8-4be6-8c71-b4e61a0b312c","action_id":"9f80ec91-24ed-42fd-b2c4-01f83903d058"} 
[2025-07-30 08:15:43] local.INFO: Starting executeAction {"job_id":"9f80fa1d-5578-4e5e-b274-ded491ebe520","stage_id":"9f80ec91-59a8-4be6-8c71-b4e61a0b312c","action_id":"9f80ec91-24ed-42fd-b2c4-01f83903d058","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb00489","pending_approval_id":"9f80fa1d-7f7d-4220-b616-7d96b7cd78b8","condition_ids":"[\"9f80ec91-5a5d-4b73-8ff3-b90a638674d1\"]","email_condition_ids":"[]"} 
[2025-07-30 08:16:31] local.INFO: Starting executeAction {"job_id":"9f80fa1d-5578-4e5e-b274-ded491ebe520","stage_id":"9f80ec91-59a8-4be6-8c71-b4e61a0b312c","action_id":"9f80ec91-24ed-42fd-b2c4-01f83903d058","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb00489","pending_approval_id":"9f80fa1d-7f7d-4220-b616-7d96b7cd78b8","condition_ids":"[\"9f80ec91-5a5d-4b73-8ff3-b90a638674d1\"]","email_condition_ids":"[]"} 
[2025-07-30 08:17:47] local.INFO: Starting executeAction {"job_id":"9f80fa1d-5578-4e5e-b274-ded491ebe520","stage_id":"9f80ec91-59a8-4be6-8c71-b4e61a0b312c","action_id":"9f80ec91-24ed-42fd-b2c4-01f83903d058","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb00489","pending_approval_id":"9f80fa1d-7f7d-4220-b616-7d96b7cd78b8","condition_ids":"[\"9f80ec91-5a5d-4b73-8ff3-b90a638674d1\"]","email_condition_ids":"[]"} 
[2025-07-30 08:19:09] local.INFO: Starting executeAction {"job_id":"9f80fa1d-5578-4e5e-b274-ded491ebe520","stage_id":"9f80ec91-59a8-4be6-8c71-b4e61a0b312c","action_id":"9f80ec91-24ed-42fd-b2c4-01f83903d058","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb00489","pending_approval_id":"9f80fa1d-7f7d-4220-b616-7d96b7cd78b8","condition_ids":"[\"9f80ec91-5a5d-4b73-8ff3-b90a638674d1\"]","email_condition_ids":"[]"} 
[2025-07-30 08:19:43] local.INFO: Starting executeAction {"job_id":"9f80fa1d-5578-4e5e-b274-ded491ebe520","stage_id":"9f80ec91-59a8-4be6-8c71-b4e61a0b312c","action_id":"9f80ec91-24ed-42fd-b2c4-01f83903d058","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb00489","pending_approval_id":"9f80fa1d-7f7d-4220-b616-7d96b7cd78b8","condition_ids":"[\"9f80ec91-5a5d-4b73-8ff3-b90a638674d1\"]","email_condition_ids":"[]"} 
[2025-07-30 08:20:03] local.INFO: Starting executeAction {"job_id":"9f80fa1d-5578-4e5e-b274-ded491ebe520","stage_id":"9f80ec91-59a8-4be6-8c71-b4e61a0b312c","action_id":"9f80ec91-24ed-42fd-b2c4-01f83903d058","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb00489","pending_approval_id":"9f80fa1d-7f7d-4220-b616-7d96b7cd78b8","condition_ids":"[\"9f80ec91-5a5d-4b73-8ff3-b90a638674d1\"]","email_condition_ids":"[]"} 
[2025-07-30 08:20:49] local.INFO: Starting executeAction {"job_id":"9f80fa1d-5578-4e5e-b274-ded491ebe520","stage_id":"9f80ec91-59a8-4be6-8c71-b4e61a0b312c","action_id":"9f80ec91-24ed-42fd-b2c4-01f83903d058","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb00489","pending_approval_id":"9f80fa1d-7f7d-4220-b616-7d96b7cd78b8","condition_ids":"[\"9f80ec91-5a5d-4b73-8ff3-b90a638674d1\"]","email_condition_ids":"[]"} 
[2025-07-30 08:21:43] local.INFO: Starting executeAction {"job_id":"9f80fa1d-5578-4e5e-b274-ded491ebe520","stage_id":"9f80ec91-59a8-4be6-8c71-b4e61a0b312c","action_id":"9f80ec91-24ed-42fd-b2c4-01f83903d058","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb00489","pending_approval_id":"9f80fa1d-7f7d-4220-b616-7d96b7cd78b8","condition_ids":"[\"9f80ec91-5a5d-4b73-8ff3-b90a638674d1\"]","email_condition_ids":"[]"} 
[2025-07-30 08:32:10] local.INFO: Starting executeAction {"job_id":"9f80fa1d-5578-4e5e-b274-ded491ebe520","stage_id":"9f80ec91-59a8-4be6-8c71-b4e61a0b312c","action_id":"9f80ec91-24ed-42fd-b2c4-01f83903d058","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb00489","pending_approval_id":"9f80fa1d-7f7d-4220-b616-7d96b7cd78b8","condition_ids":"[\"9f80ec91-5a5d-4b73-8ff3-b90a638674d1\"]","email_condition_ids":"[]"} 
[2025-07-30 08:34:24] local.INFO: Starting executeAction {"job_id":"9f80fa1d-5578-4e5e-b274-ded491ebe520","stage_id":"9f80ec91-59a8-4be6-8c71-b4e61a0b312c","action_id":"9f80ec91-24ed-42fd-b2c4-01f83903d058","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb00489","pending_approval_id":"9f80fa1d-7f7d-4220-b616-7d96b7cd78b8","condition_ids":"[\"9f80ec91-5a5d-4b73-8ff3-b90a638674d1\"]","email_condition_ids":"[]"} 
[2025-07-30 08:35:27] local.INFO: Starting executeAction {"job_id":"9f80fa1d-5578-4e5e-b274-ded491ebe520","stage_id":"9f80ec91-59a8-4be6-8c71-b4e61a0b312c","action_id":"9f80ec91-24ed-42fd-b2c4-01f83903d058","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb00489","pending_approval_id":"9f80fa1d-7f7d-4220-b616-7d96b7cd78b8","condition_ids":"[\"9f80ec91-5a5d-4b73-8ff3-b90a638674d1\"]","email_condition_ids":"[]"} 
[2025-07-30 08:46:53] local.INFO: Starting executeAction {"job_id":"9f80fa1d-5578-4e5e-b274-ded491ebe520","stage_id":"9f80ec91-59a8-4be6-8c71-b4e61a0b312c","action_id":"9f80ec91-24ed-42fd-b2c4-01f83903d058","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb00489","pending_approval_id":"9f80fa1d-7f7d-4220-b616-7d96b7cd78b8","condition_ids":"[\"9f80ec91-5a5d-4b73-8ff3-b90a638674d1\"]","email_condition_ids":"[]"} 
[2025-07-30 08:53:41] local.INFO: Starting executeAction {"job_id":"9f80fa1d-5578-4e5e-b274-ded491ebe520","stage_id":"9f80ec91-59a8-4be6-8c71-b4e61a0b312c","action_id":"9f80ec91-24ed-42fd-b2c4-01f83903d058","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb00489","pending_approval_id":"9f80fa1d-7f7d-4220-b616-7d96b7cd78b8","condition_ids":"[\"9f80ec91-5a5d-4b73-8ff3-b90a638674d1\"]","email_condition_ids":"[]"} 
[2025-07-30 09:31:06] local.ERROR: The resource owner or authorization server denied the request. {"exception":"[object] (League\\OAuth2\\Server\\Exception\\OAuthServerException(code: 9): The resource owner or authorization server denied the request. at C:\\xampp\\htdocs\\tvnas-app\\vendor\\league\\oauth2-server\\src\\Exception\\OAuthServerException.php:243)
[stacktrace]
#0 C:\\xampp\\htdocs\\tvnas-app\\vendor\\league\\oauth2-server\\src\\AuthorizationValidators\\BearerTokenValidator.php(123): League\\OAuth2\\Server\\Exception\\OAuthServerException::accessDenied('Access token ha...')
#1 C:\\xampp\\htdocs\\tvnas-app\\vendor\\league\\oauth2-server\\src\\ResourceServer.php(84): League\\OAuth2\\Server\\AuthorizationValidators\\BearerTokenValidator->validateAuthorization(Object(Nyholm\\Psr7\\ServerRequest))
#2 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(232): League\\OAuth2\\Server\\ResourceServer->validateAuthenticatedRequest(Object(Nyholm\\Psr7\\ServerRequest))
#3 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(178): Laravel\\Passport\\Guards\\TokenGuard->getPsrRequestViaBearerToken(Object(Illuminate\\Http\\Request))
#4 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(120): Laravel\\Passport\\Guards\\TokenGuard->authenticateViaBearerToken(Object(Illuminate\\Http\\Request))
#5 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php(56): Laravel\\Passport\\Guards\\TokenGuard->user()
#6 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(83): Laravel\\Passport\\Guards\\TokenGuard->check()
#7 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(62): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#8 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#9 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#11 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#12 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#13 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#14 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#15 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1188): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#36 C:\\xampp\\htdocs\\tvnas-app\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\tvnas-app\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#38 {main}
"} 
[2025-07-30 09:31:14] local.ERROR: The resource owner or authorization server denied the request. {"exception":"[object] (League\\OAuth2\\Server\\Exception\\OAuthServerException(code: 9): The resource owner or authorization server denied the request. at C:\\xampp\\htdocs\\tvnas-app\\vendor\\league\\oauth2-server\\src\\Exception\\OAuthServerException.php:243)
[stacktrace]
#0 C:\\xampp\\htdocs\\tvnas-app\\vendor\\league\\oauth2-server\\src\\AuthorizationValidators\\BearerTokenValidator.php(123): League\\OAuth2\\Server\\Exception\\OAuthServerException::accessDenied('Access token ha...')
#1 C:\\xampp\\htdocs\\tvnas-app\\vendor\\league\\oauth2-server\\src\\ResourceServer.php(84): League\\OAuth2\\Server\\AuthorizationValidators\\BearerTokenValidator->validateAuthorization(Object(Nyholm\\Psr7\\ServerRequest))
#2 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(232): League\\OAuth2\\Server\\ResourceServer->validateAuthenticatedRequest(Object(Nyholm\\Psr7\\ServerRequest))
#3 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(178): Laravel\\Passport\\Guards\\TokenGuard->getPsrRequestViaBearerToken(Object(Illuminate\\Http\\Request))
#4 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(120): Laravel\\Passport\\Guards\\TokenGuard->authenticateViaBearerToken(Object(Illuminate\\Http\\Request))
#5 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php(56): Laravel\\Passport\\Guards\\TokenGuard->user()
#6 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(83): Laravel\\Passport\\Guards\\TokenGuard->check()
#7 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(62): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#8 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#9 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#11 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#12 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#13 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#14 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#15 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1188): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#36 C:\\xampp\\htdocs\\tvnas-app\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\tvnas-app\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#38 {main}
"} 
[2025-07-30 09:34:58] local.ERROR: The resource owner or authorization server denied the request. {"exception":"[object] (League\\OAuth2\\Server\\Exception\\OAuthServerException(code: 9): The resource owner or authorization server denied the request. at C:\\xampp\\htdocs\\tvnas-app\\vendor\\league\\oauth2-server\\src\\Exception\\OAuthServerException.php:243)
[stacktrace]
#0 C:\\xampp\\htdocs\\tvnas-app\\vendor\\league\\oauth2-server\\src\\AuthorizationValidators\\BearerTokenValidator.php(123): League\\OAuth2\\Server\\Exception\\OAuthServerException::accessDenied('Access token ha...')
#1 C:\\xampp\\htdocs\\tvnas-app\\vendor\\league\\oauth2-server\\src\\ResourceServer.php(84): League\\OAuth2\\Server\\AuthorizationValidators\\BearerTokenValidator->validateAuthorization(Object(Nyholm\\Psr7\\ServerRequest))
#2 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(232): League\\OAuth2\\Server\\ResourceServer->validateAuthenticatedRequest(Object(Nyholm\\Psr7\\ServerRequest))
#3 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(178): Laravel\\Passport\\Guards\\TokenGuard->getPsrRequestViaBearerToken(Object(Illuminate\\Http\\Request))
#4 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(120): Laravel\\Passport\\Guards\\TokenGuard->authenticateViaBearerToken(Object(Illuminate\\Http\\Request))
#5 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php(56): Laravel\\Passport\\Guards\\TokenGuard->user()
#6 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(83): Laravel\\Passport\\Guards\\TokenGuard->check()
#7 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(62): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#8 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#9 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#11 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#12 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#13 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#14 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#15 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1188): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#36 C:\\xampp\\htdocs\\tvnas-app\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\tvnas-app\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#38 {main}
"} 
[2025-07-30 09:34:58] local.ERROR: The resource owner or authorization server denied the request. {"exception":"[object] (League\\OAuth2\\Server\\Exception\\OAuthServerException(code: 9): The resource owner or authorization server denied the request. at C:\\xampp\\htdocs\\tvnas-app\\vendor\\league\\oauth2-server\\src\\Exception\\OAuthServerException.php:243)
[stacktrace]
#0 C:\\xampp\\htdocs\\tvnas-app\\vendor\\league\\oauth2-server\\src\\AuthorizationValidators\\BearerTokenValidator.php(123): League\\OAuth2\\Server\\Exception\\OAuthServerException::accessDenied('Access token ha...')
#1 C:\\xampp\\htdocs\\tvnas-app\\vendor\\league\\oauth2-server\\src\\ResourceServer.php(84): League\\OAuth2\\Server\\AuthorizationValidators\\BearerTokenValidator->validateAuthorization(Object(Nyholm\\Psr7\\ServerRequest))
#2 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(232): League\\OAuth2\\Server\\ResourceServer->validateAuthenticatedRequest(Object(Nyholm\\Psr7\\ServerRequest))
#3 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(178): Laravel\\Passport\\Guards\\TokenGuard->getPsrRequestViaBearerToken(Object(Illuminate\\Http\\Request))
#4 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(120): Laravel\\Passport\\Guards\\TokenGuard->authenticateViaBearerToken(Object(Illuminate\\Http\\Request))
#5 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php(56): Laravel\\Passport\\Guards\\TokenGuard->user()
#6 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(83): Laravel\\Passport\\Guards\\TokenGuard->check()
#7 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(62): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#8 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#9 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#11 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#12 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#13 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#14 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#15 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1188): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#36 C:\\xampp\\htdocs\\tvnas-app\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\tvnas-app\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#38 {main}
"} 
[2025-07-30 09:34:58] local.ERROR: The resource owner or authorization server denied the request. {"exception":"[object] (League\\OAuth2\\Server\\Exception\\OAuthServerException(code: 9): The resource owner or authorization server denied the request. at C:\\xampp\\htdocs\\tvnas-app\\vendor\\league\\oauth2-server\\src\\Exception\\OAuthServerException.php:243)
[stacktrace]
#0 C:\\xampp\\htdocs\\tvnas-app\\vendor\\league\\oauth2-server\\src\\AuthorizationValidators\\BearerTokenValidator.php(123): League\\OAuth2\\Server\\Exception\\OAuthServerException::accessDenied('Access token ha...')
#1 C:\\xampp\\htdocs\\tvnas-app\\vendor\\league\\oauth2-server\\src\\ResourceServer.php(84): League\\OAuth2\\Server\\AuthorizationValidators\\BearerTokenValidator->validateAuthorization(Object(Nyholm\\Psr7\\ServerRequest))
#2 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(232): League\\OAuth2\\Server\\ResourceServer->validateAuthenticatedRequest(Object(Nyholm\\Psr7\\ServerRequest))
#3 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(178): Laravel\\Passport\\Guards\\TokenGuard->getPsrRequestViaBearerToken(Object(Illuminate\\Http\\Request))
#4 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(120): Laravel\\Passport\\Guards\\TokenGuard->authenticateViaBearerToken(Object(Illuminate\\Http\\Request))
#5 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php(56): Laravel\\Passport\\Guards\\TokenGuard->user()
#6 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(83): Laravel\\Passport\\Guards\\TokenGuard->check()
#7 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(62): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#8 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#9 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#11 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#12 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#13 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#14 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#15 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1188): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#36 C:\\xampp\\htdocs\\tvnas-app\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\tvnas-app\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#38 {main}
"} 
[2025-07-31 07:58:34] local.INFO: Starting executeAction {"job_id":"9f83edad-8349-4d19-9d55-11b52afe3368","stage_id":"9f83ec78-74cf-49b1-830f-c867370d9daf","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb00489","pending_approval_id":"9f83edae-0020-4105-b660-267facbf1871","condition_ids":"[]","email_condition_ids":"[]"} 
[2025-07-31 07:58:36] local.INFO: ExecuteAction completed successfully {"job_id":"9f83edad-8349-4d19-9d55-11b52afe3368","stage_id":"9f83ec78-74cf-49b1-830f-c867370d9daf","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e"} 
[2025-07-31 08:05:44] local.INFO: Starting executeAction {"job_id":"9f83edad-8349-4d19-9d55-11b52afe3368","stage_id":"9f83ec79-55ac-4ea1-8755-e25ac4ed88c1","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb00370","pending_approval_id":"9f84b6cf-abf7-4134-ab62-0410396ebf29","condition_ids":"[\"9f83ec7a-005f-4df8-bac5-a1dfed4fe99e\"]","email_condition_ids":"[]"} 
[2025-07-31 08:05:44] local.INFO: ExecuteAction completed successfully {"job_id":"9f83edad-8349-4d19-9d55-11b52afe3368","stage_id":"9f83ec79-55ac-4ea1-8755-e25ac4ed88c1","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e"} 
[2025-07-31 08:06:32] local.INFO: Starting executeAction {"job_id":"9f83edad-8349-4d19-9d55-11b52afe3368","stage_id":"9f83ec79-55ac-4ea1-8755-e25ac4ed88c1","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb00370","pending_approval_id":"9f84b6cf-abf7-4134-ab62-0410396ebf29","condition_ids":"[\"9f83ec7a-240e-4bfc-aa36-fa94441f9086\"]","email_condition_ids":"[]"} 
[2025-07-31 08:06:32] local.INFO: ExecuteAction completed successfully {"job_id":"9f83edad-8349-4d19-9d55-11b52afe3368","stage_id":"9f83ec79-55ac-4ea1-8755-e25ac4ed88c1","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e"} 
[2025-07-31 08:06:58] local.INFO: Starting executeAction {"job_id":"9f83edad-8349-4d19-9d55-11b52afe3368","stage_id":"9f83ec79-55ac-4ea1-8755-e25ac4ed88c1","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb00370","pending_approval_id":"9f84b6cf-abf7-4134-ab62-0410396ebf29","condition_ids":"[\"9f83ec79-b31f-44b6-b185-ad1c395770c0\"]","email_condition_ids":"[]"} 
[2025-07-31 08:06:58] local.INFO: ExecuteAction completed successfully {"job_id":"9f83edad-8349-4d19-9d55-11b52afe3368","stage_id":"9f83ec79-55ac-4ea1-8755-e25ac4ed88c1","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e"} 
[2025-07-31 08:08:02] local.INFO: Starting executeAction {"job_id":"9f83edad-8349-4d19-9d55-11b52afe3368","stage_id":"9f83ec79-55ac-4ea1-8755-e25ac4ed88c1","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb00370","pending_approval_id":"9f84b6cf-abf7-4134-ab62-0410396ebf29","condition_ids":"[\"9f83ec79-b31f-44b6-b185-ad1c395770c0\"]","email_condition_ids":"[]"} 
[2025-07-31 08:08:19] local.INFO: Starting executeAction {"job_id":"9f83edad-8349-4d19-9d55-11b52afe3368","stage_id":"9f83ec79-55ac-4ea1-8755-e25ac4ed88c1","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb00370","pending_approval_id":"9f84b6cf-abf7-4134-ab62-0410396ebf29","condition_ids":"[\"9f83ec7a-005f-4df8-bac5-a1dfed4fe99e\"]","email_condition_ids":"[]"} 
[2025-07-31 08:11:05] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:83)
[stacktrace]
#0 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(380): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(NULL)
#1 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(81): tap(NULL, Object(Closure))
#2 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(931): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(815): Illuminate\\Container\\Container->build(Object(Closure))
#6 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1046): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1028): Illuminate\\Container\\Container->make('encrypter', Array)
#9 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\PassportServiceProvider.php(357): Illuminate\\Foundation\\Application->make('encrypter')
#10 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\PassportServiceProvider.php(337): Laravel\\Passport\\PassportServiceProvider->makeGuard(Array)
#11 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(113): Laravel\\Passport\\PassportServiceProvider->Laravel\\Passport\\{closure}(Object(Illuminate\\Foundation\\Application), 'api', Array)
#12 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(90): Illuminate\\Auth\\AuthManager->callCustomCreator('api', Array)
#13 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(70): Illuminate\\Auth\\AuthManager->resolve('api')
#14 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(83): Illuminate\\Auth\\AuthManager->guard('api')
#15 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(62): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#16 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#17 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1188): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#44 C:\\xampp\\htdocs\\tvnas-app\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\tvnas-app\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#46 {main}
"} 
[2025-07-31 08:11:33] local.INFO: Starting executeAction {"job_id":"9f83edad-8349-4d19-9d55-11b52afe3368","stage_id":"9f83ec79-55ac-4ea1-8755-e25ac4ed88c1","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb00370","pending_approval_id":"9f84b6cf-abf7-4134-ab62-0410396ebf29","condition_ids":"[\"9f83ec79-b31f-44b6-b185-ad1c395770c0\"]","email_condition_ids":"[]"} 
[2025-07-31 08:12:04] local.INFO: Starting executeAction {"job_id":"9f83edad-8349-4d19-9d55-11b52afe3368","stage_id":"9f83ec79-55ac-4ea1-8755-e25ac4ed88c1","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb00370","pending_approval_id":"9f84b6cf-abf7-4134-ab62-0410396ebf29","condition_ids":"[\"9f83ec79-b31f-44b6-b185-ad1c395770c0\"]","email_condition_ids":"[]"} 
[2025-07-31 08:12:47] local.INFO: Starting executeAction {"job_id":"9f83edad-8349-4d19-9d55-11b52afe3368","stage_id":"9f83ec79-55ac-4ea1-8755-e25ac4ed88c1","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb00370","pending_approval_id":"9f84b6cf-abf7-4134-ab62-0410396ebf29","condition_ids":"[\"9f83ec79-b31f-44b6-b185-ad1c395770c0\"]","email_condition_ids":"[]"} 
[2025-07-31 08:15:35] local.INFO: Starting executeAction {"job_id":"9f83edad-8349-4d19-9d55-11b52afe3368","stage_id":"9f83ec79-55ac-4ea1-8755-e25ac4ed88c1","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb00370","pending_approval_id":"9f84b6cf-abf7-4134-ab62-0410396ebf29","condition_ids":"[\"9f83ec79-b31f-44b6-b185-ad1c395770c0\"]","email_condition_ids":"[]"} 
[2025-07-31 08:15:36] local.ERROR: in_array(): Argument #2 ($haystack) must be of type array, string given {"userId":"9f793fc2-3089-4dbd-9215-fdd68fb00370","exception":"[object] (TypeError(code: 0): in_array(): Argument #2 ($haystack) must be of type array, string given at C:\\xampp\\htdocs\\tvnas-app\\app\\Services\\ProcessConditionService.php:322)
[stacktrace]
#0 C:\\xampp\\htdocs\\tvnas-app\\app\\Services\\ProcessConditionService.php(322): in_array('Chuy\\xE1\\xBB\\x83n c\\xC3\\xB9ng ...', 'Chuy\\xE1\\xBB\\x83n c\\xC3\\xB9ng ...')
#1 C:\\xampp\\htdocs\\tvnas-app\\app\\Services\\ProcessConditionService.php(114): App\\Services\\ProcessConditionService->evaluateSingleCondition(Array, Array, 'form')
#2 C:\\xampp\\htdocs\\tvnas-app\\app\\Services\\ProcessConditionService.php(82): App\\Services\\ProcessConditionService->evaluateInternalBlockAndLogic(Array, Array, 'form')
#3 C:\\xampp\\htdocs\\tvnas-app\\app\\Services\\ProcessConditionService.php(29): App\\Services\\ProcessConditionService->checkAllFilterConditions(Array, Array, Array)
#4 C:\\xampp\\htdocs\\tvnas-app\\app\\Services\\StageTransitionService.php(37): App\\Services\\ProcessConditionService->checkCondition(Array, Object(App\\Models\\Process), Array, Object(App\\Models\\User))
#5 C:\\xampp\\htdocs\\tvnas-app\\app\\Services\\WorkflowProcessService.php(53): App\\Services\\StageTransitionService->getFromStageInStageTransitions(Array, Object(App\\Models\\Process), '9f83ec79-55ac-4...', '9f83ec79-6b21-4...', Object(App\\Models\\ProcessVersion), Object(App\\Models\\User), Array)
#6 C:\\xampp\\htdocs\\tvnas-app\\app\\Http\\Controllers\\Job\\SaveJobController.php(355): App\\Services\\WorkflowProcessService->actionProcess('9f83edad-8349-4...', Object(Illuminate\\Database\\Eloquent\\Collection), Object(App\\Models\\Process), '9f83ec79-55ac-4...', '9f83ec79-6b21-4...', Object(App\\Models\\ProcessVersion), NULL, '9f84b6cf-abf7-4...', '[\"9f83ec79-b31f...', '[]')
#7 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Job\\SaveJobController->executeAction(Object(Illuminate\\Http\\Request))
#8 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(264): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Job\\SaveJobController), 'executeAction')
#9 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(210): Illuminate\\Routing\\Route->runController()
#10 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#11 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\xampp\\htdocs\\tvnas-app\\app\\Http\\Middleware\\IdentifyTenant.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\IdentifyTenant->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authorize.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authorize->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'VIEW-JOB')
#16 C:\\xampp\\htdocs\\tvnas-app\\app\\Http\\Middleware\\SetLocale.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SetLocale->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(64): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#22 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#24 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#26 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#47 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#48 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1188): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#49 C:\\xampp\\htdocs\\tvnas-app\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#50 C:\\xampp\\htdocs\\tvnas-app\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#51 {main}
"} 
[2025-07-31 08:21:13] local.INFO: Starting executeAction {"job_id":"9f83edad-8349-4d19-9d55-11b52afe3368","stage_id":"9f83ec79-55ac-4ea1-8755-e25ac4ed88c1","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb00370","pending_approval_id":"9f84b6cf-abf7-4134-ab62-0410396ebf29","condition_ids":"[\"9f83ec79-b31f-44b6-b185-ad1c395770c0\"]","email_condition_ids":"[]"} 
[2025-07-31 08:31:14] local.INFO: Starting executeAction {"job_id":"9f83edad-8349-4d19-9d55-11b52afe3368","stage_id":"9f83ec79-55ac-4ea1-8755-e25ac4ed88c1","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb00370","pending_approval_id":"9f84b6cf-abf7-4134-ab62-0410396ebf29","condition_ids":"[\"9f83ec79-b31f-44b6-b185-ad1c395770c0\"]","email_condition_ids":"[]"} 
[2025-07-31 08:31:21] local.INFO: Starting executeAction {"job_id":"9f83edad-8349-4d19-9d55-11b52afe3368","stage_id":"9f83ec79-55ac-4ea1-8755-e25ac4ed88c1","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb00370","pending_approval_id":"9f84b6cf-abf7-4134-ab62-0410396ebf29","condition_ids":"[\"9f83ec7a-005f-4df8-bac5-a1dfed4fe99e\"]","email_condition_ids":"[]"} 
[2025-07-31 08:31:31] local.INFO: Starting executeAction {"job_id":"9f83edad-8349-4d19-9d55-11b52afe3368","stage_id":"9f83ec79-55ac-4ea1-8755-e25ac4ed88c1","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb00370","pending_approval_id":"9f84b6cf-abf7-4134-ab62-0410396ebf29","condition_ids":"[\"9f83ec7a-133c-4eb2-8067-818ab9042645\"]","email_condition_ids":"[]"} 
[2025-07-31 08:50:09] local.INFO: Starting executeAction {"job_id":"9f83edad-8349-4d19-9d55-11b52afe3368","stage_id":"9f83ec79-55ac-4ea1-8755-e25ac4ed88c1","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb00370","pending_approval_id":"9f84b6cf-abf7-4134-ab62-0410396ebf29","condition_ids":"[\"9f83ec79-b31f-44b6-b185-ad1c395770c0\"]","email_condition_ids":"[]"} 
[2025-07-31 08:50:10] local.INFO: ExecuteAction completed successfully {"job_id":"9f83edad-8349-4d19-9d55-11b52afe3368","stage_id":"9f83ec79-55ac-4ea1-8755-e25ac4ed88c1","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e"} 
[2025-07-31 08:51:08] local.ERROR: This password does not use the Bcrypt algorithm. {"exception":"[object] (RuntimeException(code: 0): This password does not use the Bcrypt algorithm. at C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Hashing\\BcryptHasher.php:75)
[stacktrace]
#0 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Hashing\\HashManager.php(76): Illuminate\\Hashing\\BcryptHasher->check(Object(SensitiveParameterValue), 'F051693', Array)
#1 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php(159): Illuminate\\Hashing\\HashManager->check(Object(SensitiveParameterValue), 'F051693')
#2 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php(454): Illuminate\\Auth\\EloquentUserProvider->validateCredentials(Object(App\\Models\\User), Object(SensitiveParameterValue))
#3 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Timebox.php(34): Illuminate\\Auth\\SessionGuard->Illuminate\\Auth\\{closure}(Object(Illuminate\\Support\\Timebox))
#4 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php(453): Illuminate\\Support\\Timebox->call(Object(Closure), 200000)
#5 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php(398): Illuminate\\Auth\\SessionGuard->hasValidCredentials(Object(App\\Models\\User), Array)
#6 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(341): Illuminate\\Auth\\SessionGuard->attempt(Array)
#7 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(358): Illuminate\\Auth\\AuthManager->__call('attempt', Array)
#8 C:\\xampp\\htdocs\\tvnas-app\\app\\Http\\Controllers\\Api\\Auth\\AuthController.php(67): Illuminate\\Support\\Facades\\Facade::__callStatic('attempt', Array)
#9 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Api\\Auth\\AuthController->login(Object(Illuminate\\Http\\Request))
#10 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(264): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Api\\Auth\\AuthController), 'login')
#11 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(210): Illuminate\\Routing\\Route->runController()
#12 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#13 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\xampp\\htdocs\\tvnas-app\\app\\Http\\Middleware\\SetLocale.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SetLocale->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#20 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#21 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#22 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#24 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#43 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#44 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1188): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\tvnas-app\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#46 C:\\xampp\\htdocs\\tvnas-app\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#47 {main}
"} 
[2025-07-31 08:52:04] local.INFO: Starting executeAction {"job_id":"9f83edad-8349-4d19-9d55-11b52afe3368","stage_id":"9f83ec79-74b9-4bd3-ae16-7930b046aa80","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb00552","pending_approval_id":"9f84c941-1ba9-41ea-b23d-3f4e117c2827","condition_ids":"[]","email_condition_ids":"[]"} 
[2025-07-31 08:52:04] local.INFO: ExecuteAction completed successfully {"job_id":"9f83edad-8349-4d19-9d55-11b52afe3368","stage_id":"9f83ec79-74b9-4bd3-ae16-7930b046aa80","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e"} 
[2025-07-31 08:52:54] local.INFO: Starting executeAction {"job_id":"9f83edad-8349-4d19-9d55-11b52afe3368","stage_id":"9f83ec79-f5a5-4d09-a1f3-0105fb1fcab9","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb00489","pending_approval_id":"9f84c9ef-acd3-4a10-9d6a-f4fd84810340","condition_ids":"[]","email_condition_ids":"[]"} 
[2025-07-31 08:52:54] local.INFO: ExecuteAction completed successfully {"job_id":"9f83edad-8349-4d19-9d55-11b52afe3368","stage_id":"9f83ec79-f5a5-4d09-a1f3-0105fb1fcab9","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e"} 
[2025-07-31 08:53:31] local.INFO: Starting executeAction {"job_id":"9f83edad-8349-4d19-9d55-11b52afe3368","stage_id":"9f83ec79-f9df-4eb7-9202-8b3e8be6b150","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb00370","pending_approval_id":"9f84ca3c-8958-454f-812a-6e7673f0305f","condition_ids":"[]","email_condition_ids":"[]"} 
[2025-07-31 08:53:31] local.INFO: ExecuteAction completed successfully {"job_id":"9f83edad-8349-4d19-9d55-11b52afe3368","stage_id":"9f83ec79-f9df-4eb7-9202-8b3e8be6b150","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e"} 
[2025-07-31 09:27:06] local.INFO: Starting executeAction {"job_id":"9f83eecd-4e2f-40e2-a8fc-c8a423bcd709","stage_id":"9f83ec78-74cf-49b1-830f-c867370d9daf","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb00489","pending_approval_id":"9f83eecd-7058-4618-8f9b-eeef0ae43fe7","condition_ids":"[]","email_condition_ids":"[]"} 
[2025-07-31 09:27:13] local.INFO: ExecuteAction completed successfully {"job_id":"9f83eecd-4e2f-40e2-a8fc-c8a423bcd709","stage_id":"9f83ec78-74cf-49b1-830f-c867370d9daf","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e"} 
[2025-07-31 09:28:20] local.INFO: Starting executeAction {"job_id":"9f83eecd-4e2f-40e2-a8fc-c8a423bcd709","stage_id":"9f83ec79-55ac-4ea1-8755-e25ac4ed88c1","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb00370","pending_approval_id":"9f84d67c-410e-40aa-95ac-ee7a42c2e3c9","condition_ids":"[\"9f83ec7a-240e-4bfc-aa36-fa94441f9086\"]","email_condition_ids":"[]"} 
[2025-07-31 09:28:20] local.INFO: ExecuteAction completed successfully {"job_id":"9f83eecd-4e2f-40e2-a8fc-c8a423bcd709","stage_id":"9f83ec79-55ac-4ea1-8755-e25ac4ed88c1","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e"} 
[2025-07-31 09:28:57] local.INFO: Starting executeAction {"job_id":"9f83eecd-4e2f-40e2-a8fc-c8a423bcd709","stage_id":"9f83ec79-55ac-4ea1-8755-e25ac4ed88c1","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb00370","pending_approval_id":"9f84d67c-410e-40aa-95ac-ee7a42c2e3c9","condition_ids":"[\"9f83ec7a-240e-4bfc-aa36-fa94441f9086\"]","email_condition_ids":"[]"} 
[2025-07-31 09:30:15] local.INFO: Starting executeAction {"job_id":"9f83eecd-4e2f-40e2-a8fc-c8a423bcd709","stage_id":"9f83ec79-55ac-4ea1-8755-e25ac4ed88c1","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb00370","pending_approval_id":"9f84d67c-410e-40aa-95ac-ee7a42c2e3c9","condition_ids":"[\"9f83ec7a-240e-4bfc-aa36-fa94441f9086\"]","email_condition_ids":"[]"} 
[2025-07-31 09:30:51] local.INFO: Starting executeAction {"job_id":"9f83eecd-4e2f-40e2-a8fc-c8a423bcd709","stage_id":"9f83ec79-55ac-4ea1-8755-e25ac4ed88c1","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb00370","pending_approval_id":"9f84d67c-410e-40aa-95ac-ee7a42c2e3c9","condition_ids":"[\"9f83ec7a-240e-4bfc-aa36-fa94441f9086\"]","email_condition_ids":"[]"} 
[2025-07-31 09:31:08] local.INFO: Starting executeAction {"job_id":"9f83eecd-4e2f-40e2-a8fc-c8a423bcd709","stage_id":"9f83ec79-55ac-4ea1-8755-e25ac4ed88c1","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb00370","pending_approval_id":"9f84d67c-410e-40aa-95ac-ee7a42c2e3c9","condition_ids":"[\"9f83ec7a-240e-4bfc-aa36-fa94441f9086\"]","email_condition_ids":"[]"} 
[2025-07-31 09:32:04] local.INFO: Starting executeAction {"job_id":"9f83eecd-4e2f-40e2-a8fc-c8a423bcd709","stage_id":"9f83ec79-55ac-4ea1-8755-e25ac4ed88c1","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb00370","pending_approval_id":"9f84d67c-410e-40aa-95ac-ee7a42c2e3c9","condition_ids":"[\"9f83ec7a-240e-4bfc-aa36-fa94441f9086\"]","email_condition_ids":"[]"} 
[2025-07-31 09:32:54] local.INFO: Starting executeAction {"job_id":"9f83eecd-4e2f-40e2-a8fc-c8a423bcd709","stage_id":"9f83ec79-55ac-4ea1-8755-e25ac4ed88c1","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb00370","pending_approval_id":"9f84d67c-410e-40aa-95ac-ee7a42c2e3c9","condition_ids":"[\"9f83ec7a-240e-4bfc-aa36-fa94441f9086\"]","email_condition_ids":"[]"} 
[2025-07-31 09:33:06] local.INFO: Starting executeAction {"job_id":"9f83eecd-4e2f-40e2-a8fc-c8a423bcd709","stage_id":"9f83ec79-55ac-4ea1-8755-e25ac4ed88c1","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb00370","pending_approval_id":"9f84d67c-410e-40aa-95ac-ee7a42c2e3c9","condition_ids":"[\"9f83ec7a-240e-4bfc-aa36-fa94441f9086\"]","email_condition_ids":"[]"} 
[2025-07-31 09:33:23] local.INFO: Starting executeAction {"job_id":"9f83eecd-4e2f-40e2-a8fc-c8a423bcd709","stage_id":"9f83ec79-55ac-4ea1-8755-e25ac4ed88c1","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb00370","pending_approval_id":"9f84d67c-410e-40aa-95ac-ee7a42c2e3c9","condition_ids":"[\"9f83ec7a-240e-4bfc-aa36-fa94441f9086\"]","email_condition_ids":"[]"} 
[2025-07-31 09:33:49] local.INFO: Starting executeAction {"job_id":"9f83eecd-4e2f-40e2-a8fc-c8a423bcd709","stage_id":"9f83ec79-55ac-4ea1-8755-e25ac4ed88c1","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb00370","pending_approval_id":"9f84d67c-410e-40aa-95ac-ee7a42c2e3c9","condition_ids":"[\"9f83ec7a-240e-4bfc-aa36-fa94441f9086\"]","email_condition_ids":"[]"} 
[2025-07-31 10:17:27] local.INFO: Starting executeAction {"job_id":"9f83eecd-4e2f-40e2-a8fc-c8a423bcd709","stage_id":"9f83ec79-55ac-4ea1-8755-e25ac4ed88c1","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb00370","pending_approval_id":"9f84d67c-410e-40aa-95ac-ee7a42c2e3c9","condition_ids":"[\"9f83ec7a-240e-4bfc-aa36-fa94441f9086\"]","email_condition_ids":"[]"} 
[2025-07-31 10:17:27] local.WARNING: No data processed in actionProcess {"job_id":"9f83eecd-4e2f-40e2-a8fc-c8a423bcd709","stage_id":"9f83ec79-55ac-4ea1-8755-e25ac4ed88c1","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e"} 
[2025-07-31 10:17:41] local.INFO: Starting executeAction {"job_id":"9f83eecd-4e2f-40e2-a8fc-c8a423bcd709","stage_id":"9f83ec79-55ac-4ea1-8755-e25ac4ed88c1","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb00370","pending_approval_id":"9f84d67c-410e-40aa-95ac-ee7a42c2e3c9","condition_ids":"[\"9f83ec7a-240e-4bfc-aa36-fa94441f9086\"]","email_condition_ids":"[]"} 
[2025-07-31 10:17:41] local.WARNING: No data processed in actionProcess {"job_id":"9f83eecd-4e2f-40e2-a8fc-c8a423bcd709","stage_id":"9f83ec79-55ac-4ea1-8755-e25ac4ed88c1","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e"} 
[2025-07-31 10:17:41] local.ERROR: ActionProcess returned false {"job_id":"9f83eecd-4e2f-40e2-a8fc-c8a423bcd709","stage_id":"9f83ec79-55ac-4ea1-8755-e25ac4ed88c1","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e"} 
[2025-07-31 10:17:54] local.INFO: Starting executeAction {"job_id":"9f83eecd-4e2f-40e2-a8fc-c8a423bcd709","stage_id":"9f83ec79-55ac-4ea1-8755-e25ac4ed88c1","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb00370","pending_approval_id":"9f84d67c-410e-40aa-95ac-ee7a42c2e3c9","condition_ids":"[\"9f83ec79-b31f-44b6-b185-ad1c395770c0\"]","email_condition_ids":"[]"} 
[2025-07-31 10:17:54] local.WARNING: No data processed in actionProcess {"job_id":"9f83eecd-4e2f-40e2-a8fc-c8a423bcd709","stage_id":"9f83ec79-55ac-4ea1-8755-e25ac4ed88c1","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e"} 
[2025-07-31 10:17:54] local.ERROR: ActionProcess returned false {"job_id":"9f83eecd-4e2f-40e2-a8fc-c8a423bcd709","stage_id":"9f83ec79-55ac-4ea1-8755-e25ac4ed88c1","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e"} 
[2025-07-31 10:18:08] local.INFO: Starting executeAction {"job_id":"9f83eecd-4e2f-40e2-a8fc-c8a423bcd709","stage_id":"9f83ec79-55ac-4ea1-8755-e25ac4ed88c1","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb00370","pending_approval_id":"9f84d67c-410e-40aa-95ac-ee7a42c2e3c9","condition_ids":"[\"9f83ec7a-005f-4df8-bac5-a1dfed4fe99e\"]","email_condition_ids":"[]"} 
[2025-07-31 10:18:08] local.INFO: ExecuteAction completed successfully {"job_id":"9f83eecd-4e2f-40e2-a8fc-c8a423bcd709","stage_id":"9f83ec79-55ac-4ea1-8755-e25ac4ed88c1","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e"} 
[2025-07-31 10:20:51] local.INFO: Starting executeAction {"job_id":"9f83eecd-4e2f-40e2-a8fc-c8a423bcd709","stage_id":"9f83ec79-ffb9-46cf-9ae1-70274a66897a","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb02888","pending_approval_id":"9f84e8b7-47f7-491b-b394-a8d9d3584f3d","condition_ids":"[]","email_condition_ids":"[]"} 
[2025-07-31 10:20:51] local.INFO: ExecuteAction completed successfully {"job_id":"9f83eecd-4e2f-40e2-a8fc-c8a423bcd709","stage_id":"9f83ec79-ffb9-46cf-9ae1-70274a66897a","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e"} 
[2025-07-31 10:22:04] local.INFO: Starting executeAction {"job_id":"9f83eecd-4e2f-40e2-a8fc-c8a423bcd709","stage_id":"9f83ec7a-047f-4614-89ff-06e865c32c6a","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb02863","pending_approval_id":"9f84e9af-c161-439c-b474-99a74c195c20","condition_ids":"[]","email_condition_ids":"[]"} 
[2025-07-31 10:22:04] local.INFO: ExecuteAction completed successfully {"job_id":"9f83eecd-4e2f-40e2-a8fc-c8a423bcd709","stage_id":"9f83ec7a-047f-4614-89ff-06e865c32c6a","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e"} 
[2025-07-31 10:26:00] local.INFO: Starting executeAction {"job_id":"9f83eecd-4e2f-40e2-a8fc-c8a423bcd709","stage_id":"9f83ec7a-074b-4c68-af5f-9e61eec0cb46","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb01173","pending_approval_id":"9f84ea20-6339-4e7e-89f5-cc479964cbac","condition_ids":"[]","email_condition_ids":"[]"} 
[2025-07-31 10:26:00] local.INFO: ExecuteAction completed successfully {"job_id":"9f83eecd-4e2f-40e2-a8fc-c8a423bcd709","stage_id":"9f83ec7a-074b-4c68-af5f-9e61eec0cb46","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e"} 
[2025-07-31 10:27:15] local.INFO: Starting executeAction {"job_id":"9f83eecd-4e2f-40e2-a8fc-c8a423bcd709","stage_id":"9f83ec79-74b9-4bd3-ae16-7930b046aa80","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb00552","pending_approval_id":"9f84eb87-6e17-413f-9817-d54828a3ca36","condition_ids":"[]","email_condition_ids":"[]"} 
[2025-07-31 10:27:15] local.INFO: ExecuteAction completed successfully {"job_id":"9f83eecd-4e2f-40e2-a8fc-c8a423bcd709","stage_id":"9f83ec79-74b9-4bd3-ae16-7930b046aa80","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e"} 
[2025-07-31 10:27:40] local.INFO: Starting executeAction {"job_id":"9f83eecd-4e2f-40e2-a8fc-c8a423bcd709","stage_id":"9f83ec79-f5a5-4d09-a1f3-0105fb1fcab9","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb00489","pending_approval_id":"9f84ebfa-8336-45c1-8c70-eed4c36bbe23","condition_ids":"[]","email_condition_ids":"[]"} 
[2025-07-31 10:27:40] local.INFO: ExecuteAction completed successfully {"job_id":"9f83eecd-4e2f-40e2-a8fc-c8a423bcd709","stage_id":"9f83ec79-f5a5-4d09-a1f3-0105fb1fcab9","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e"} 
[2025-07-31 10:28:35] local.INFO: Starting executeAction {"job_id":"9f83eecd-4e2f-40e2-a8fc-c8a423bcd709","stage_id":"9f83ec79-f9df-4eb7-9202-8b3e8be6b150","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb00370","pending_approval_id":"9f84ec20-6891-489e-8795-ee97f2d9b22e","condition_ids":"[]","email_condition_ids":"[]"} 
[2025-07-31 10:28:36] local.INFO: ExecuteAction completed successfully {"job_id":"9f83eecd-4e2f-40e2-a8fc-c8a423bcd709","stage_id":"9f83ec79-f9df-4eb7-9202-8b3e8be6b150","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e"} 
[2025-07-31 11:25:32] local.INFO: Starting executeAction {"job_id":"9f85007b-845a-4839-9ebe-130f0eedc59d","stage_id":"9f83ec78-74cf-49b1-830f-c867370d9daf","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb00489","pending_approval_id":"9f85007c-58db-4dec-b3a4-570907a3cb84","condition_ids":"[]","email_condition_ids":"[]"} 
[2025-07-31 11:25:32] local.INFO: ExecuteAction completed successfully {"job_id":"9f85007b-845a-4839-9ebe-130f0eedc59d","stage_id":"9f83ec78-74cf-49b1-830f-c867370d9daf","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e"} 
[2025-07-31 11:26:28] local.INFO: Starting executeAction {"job_id":"9f85007b-845a-4839-9ebe-130f0eedc59d","stage_id":"9f83ec79-55ac-4ea1-8755-e25ac4ed88c1","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb00370","pending_approval_id":"9f8500d1-e931-4340-9acf-bb71e815af6b","condition_ids":"[\"9f83ec7a-133c-4eb2-8067-818ab9042645\"]","email_condition_ids":"[]"} 
[2025-07-31 11:26:28] local.WARNING: No data processed in actionProcess {"job_id":"9f85007b-845a-4839-9ebe-130f0eedc59d","stage_id":"9f83ec79-55ac-4ea1-8755-e25ac4ed88c1","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e"} 
[2025-07-31 11:26:28] local.ERROR: ActionProcess returned false {"job_id":"9f85007b-845a-4839-9ebe-130f0eedc59d","stage_id":"9f83ec79-55ac-4ea1-8755-e25ac4ed88c1","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e"} 
[2025-07-31 11:26:34] local.INFO: Starting executeAction {"job_id":"9f85007b-845a-4839-9ebe-130f0eedc59d","stage_id":"9f83ec79-55ac-4ea1-8755-e25ac4ed88c1","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb00370","pending_approval_id":"9f8500d1-e931-4340-9acf-bb71e815af6b","condition_ids":"[\"9f83ec7a-240e-4bfc-aa36-fa94441f9086\"]","email_condition_ids":"[]"} 
[2025-07-31 11:26:34] local.INFO: ExecuteAction completed successfully {"job_id":"9f85007b-845a-4839-9ebe-130f0eedc59d","stage_id":"9f83ec79-55ac-4ea1-8755-e25ac4ed88c1","action_id":"9f83ec79-6b21-4613-b6b6-d87ccdc0930e"} 
[2025-07-31 14:11:48] local.ERROR: The resource owner or authorization server denied the request. {"exception":"[object] (League\\OAuth2\\Server\\Exception\\OAuthServerException(code: 9): The resource owner or authorization server denied the request. at C:\\xampp\\htdocs\\tvnas-app\\vendor\\league\\oauth2-server\\src\\Exception\\OAuthServerException.php:243)
[stacktrace]
#0 C:\\xampp\\htdocs\\tvnas-app\\vendor\\league\\oauth2-server\\src\\AuthorizationValidators\\BearerTokenValidator.php(123): League\\OAuth2\\Server\\Exception\\OAuthServerException::accessDenied('Access token ha...')
#1 C:\\xampp\\htdocs\\tvnas-app\\vendor\\league\\oauth2-server\\src\\ResourceServer.php(84): League\\OAuth2\\Server\\AuthorizationValidators\\BearerTokenValidator->validateAuthorization(Object(Nyholm\\Psr7\\ServerRequest))
#2 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(232): League\\OAuth2\\Server\\ResourceServer->validateAuthenticatedRequest(Object(Nyholm\\Psr7\\ServerRequest))
#3 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(178): Laravel\\Passport\\Guards\\TokenGuard->getPsrRequestViaBearerToken(Object(Illuminate\\Http\\Request))
#4 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(120): Laravel\\Passport\\Guards\\TokenGuard->authenticateViaBearerToken(Object(Illuminate\\Http\\Request))
#5 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php(56): Laravel\\Passport\\Guards\\TokenGuard->user()
#6 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(83): Laravel\\Passport\\Guards\\TokenGuard->check()
#7 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(62): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#8 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#9 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#11 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#12 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#13 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#14 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#15 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1188): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#36 C:\\xampp\\htdocs\\tvnas-app\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\tvnas-app\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#38 {main}
"} 
[2025-07-31 14:11:57] local.ERROR: The resource owner or authorization server denied the request. {"exception":"[object] (League\\OAuth2\\Server\\Exception\\OAuthServerException(code: 9): The resource owner or authorization server denied the request. at C:\\xampp\\htdocs\\tvnas-app\\vendor\\league\\oauth2-server\\src\\Exception\\OAuthServerException.php:243)
[stacktrace]
#0 C:\\xampp\\htdocs\\tvnas-app\\vendor\\league\\oauth2-server\\src\\AuthorizationValidators\\BearerTokenValidator.php(123): League\\OAuth2\\Server\\Exception\\OAuthServerException::accessDenied('Access token ha...')
#1 C:\\xampp\\htdocs\\tvnas-app\\vendor\\league\\oauth2-server\\src\\ResourceServer.php(84): League\\OAuth2\\Server\\AuthorizationValidators\\BearerTokenValidator->validateAuthorization(Object(Nyholm\\Psr7\\ServerRequest))
#2 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(232): League\\OAuth2\\Server\\ResourceServer->validateAuthenticatedRequest(Object(Nyholm\\Psr7\\ServerRequest))
#3 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(178): Laravel\\Passport\\Guards\\TokenGuard->getPsrRequestViaBearerToken(Object(Illuminate\\Http\\Request))
#4 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(120): Laravel\\Passport\\Guards\\TokenGuard->authenticateViaBearerToken(Object(Illuminate\\Http\\Request))
#5 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php(56): Laravel\\Passport\\Guards\\TokenGuard->user()
#6 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(83): Laravel\\Passport\\Guards\\TokenGuard->check()
#7 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(62): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#8 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#9 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#11 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#12 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#13 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#14 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#15 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1188): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#36 C:\\xampp\\htdocs\\tvnas-app\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\tvnas-app\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#38 {main}
"} 
[2025-07-31 14:14:02] local.ERROR: The resource owner or authorization server denied the request. {"exception":"[object] (League\\OAuth2\\Server\\Exception\\OAuthServerException(code: 9): The resource owner or authorization server denied the request. at C:\\xampp\\htdocs\\tvnas-app\\vendor\\league\\oauth2-server\\src\\Exception\\OAuthServerException.php:243)
[stacktrace]
#0 C:\\xampp\\htdocs\\tvnas-app\\vendor\\league\\oauth2-server\\src\\AuthorizationValidators\\BearerTokenValidator.php(123): League\\OAuth2\\Server\\Exception\\OAuthServerException::accessDenied('Access token ha...')
#1 C:\\xampp\\htdocs\\tvnas-app\\vendor\\league\\oauth2-server\\src\\ResourceServer.php(84): League\\OAuth2\\Server\\AuthorizationValidators\\BearerTokenValidator->validateAuthorization(Object(Nyholm\\Psr7\\ServerRequest))
#2 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(232): League\\OAuth2\\Server\\ResourceServer->validateAuthenticatedRequest(Object(Nyholm\\Psr7\\ServerRequest))
#3 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(178): Laravel\\Passport\\Guards\\TokenGuard->getPsrRequestViaBearerToken(Object(Illuminate\\Http\\Request))
#4 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(120): Laravel\\Passport\\Guards\\TokenGuard->authenticateViaBearerToken(Object(Illuminate\\Http\\Request))
#5 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php(56): Laravel\\Passport\\Guards\\TokenGuard->user()
#6 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(83): Laravel\\Passport\\Guards\\TokenGuard->check()
#7 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(62): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#8 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#9 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#11 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#12 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#13 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#14 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#15 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1188): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#36 C:\\xampp\\htdocs\\tvnas-app\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\tvnas-app\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#38 {main}
"} 
[2025-07-31 14:14:05] local.ERROR: The resource owner or authorization server denied the request. {"exception":"[object] (League\\OAuth2\\Server\\Exception\\OAuthServerException(code: 9): The resource owner or authorization server denied the request. at C:\\xampp\\htdocs\\tvnas-app\\vendor\\league\\oauth2-server\\src\\Exception\\OAuthServerException.php:243)
[stacktrace]
#0 C:\\xampp\\htdocs\\tvnas-app\\vendor\\league\\oauth2-server\\src\\AuthorizationValidators\\BearerTokenValidator.php(123): League\\OAuth2\\Server\\Exception\\OAuthServerException::accessDenied('Access token ha...')
#1 C:\\xampp\\htdocs\\tvnas-app\\vendor\\league\\oauth2-server\\src\\ResourceServer.php(84): League\\OAuth2\\Server\\AuthorizationValidators\\BearerTokenValidator->validateAuthorization(Object(Nyholm\\Psr7\\ServerRequest))
#2 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(232): League\\OAuth2\\Server\\ResourceServer->validateAuthenticatedRequest(Object(Nyholm\\Psr7\\ServerRequest))
#3 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(178): Laravel\\Passport\\Guards\\TokenGuard->getPsrRequestViaBearerToken(Object(Illuminate\\Http\\Request))
#4 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(120): Laravel\\Passport\\Guards\\TokenGuard->authenticateViaBearerToken(Object(Illuminate\\Http\\Request))
#5 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php(56): Laravel\\Passport\\Guards\\TokenGuard->user()
#6 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(83): Laravel\\Passport\\Guards\\TokenGuard->check()
#7 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(62): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#8 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#9 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#11 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#12 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#13 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#14 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#15 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1188): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#36 C:\\xampp\\htdocs\\tvnas-app\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\tvnas-app\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#38 {main}
"} 
[2025-07-31 14:14:08] local.ERROR: The resource owner or authorization server denied the request. {"exception":"[object] (League\\OAuth2\\Server\\Exception\\OAuthServerException(code: 9): The resource owner or authorization server denied the request. at C:\\xampp\\htdocs\\tvnas-app\\vendor\\league\\oauth2-server\\src\\Exception\\OAuthServerException.php:243)
[stacktrace]
#0 C:\\xampp\\htdocs\\tvnas-app\\vendor\\league\\oauth2-server\\src\\AuthorizationValidators\\BearerTokenValidator.php(123): League\\OAuth2\\Server\\Exception\\OAuthServerException::accessDenied('Access token ha...')
#1 C:\\xampp\\htdocs\\tvnas-app\\vendor\\league\\oauth2-server\\src\\ResourceServer.php(84): League\\OAuth2\\Server\\AuthorizationValidators\\BearerTokenValidator->validateAuthorization(Object(Nyholm\\Psr7\\ServerRequest))
#2 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(232): League\\OAuth2\\Server\\ResourceServer->validateAuthenticatedRequest(Object(Nyholm\\Psr7\\ServerRequest))
#3 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(178): Laravel\\Passport\\Guards\\TokenGuard->getPsrRequestViaBearerToken(Object(Illuminate\\Http\\Request))
#4 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(120): Laravel\\Passport\\Guards\\TokenGuard->authenticateViaBearerToken(Object(Illuminate\\Http\\Request))
#5 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php(56): Laravel\\Passport\\Guards\\TokenGuard->user()
#6 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(83): Laravel\\Passport\\Guards\\TokenGuard->check()
#7 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(62): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#8 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#9 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#11 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#12 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#13 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#14 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#15 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1188): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#36 C:\\xampp\\htdocs\\tvnas-app\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\tvnas-app\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#38 {main}
"} 
[2025-08-01 09:22:32] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:83)
[stacktrace]
#0 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(380): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(NULL)
#1 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(81): tap(NULL, Object(Closure))
#2 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(931): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(815): Illuminate\\Container\\Container->build(Object(Closure))
#6 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1046): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1028): Illuminate\\Container\\Container->make('encrypter', Array)
#9 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\PassportServiceProvider.php(357): Illuminate\\Foundation\\Application->make('encrypter')
#10 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\PassportServiceProvider.php(337): Laravel\\Passport\\PassportServiceProvider->makeGuard(Array)
#11 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(113): Laravel\\Passport\\PassportServiceProvider->Laravel\\Passport\\{closure}(Object(Illuminate\\Foundation\\Application), 'api', Array)
#12 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(90): Illuminate\\Auth\\AuthManager->callCustomCreator('api', Array)
#13 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(70): Illuminate\\Auth\\AuthManager->resolve('api')
#14 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(83): Illuminate\\Auth\\AuthManager->guard('api')
#15 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(62): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#16 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#17 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1188): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#44 C:\\xampp\\htdocs\\tvnas-app\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\tvnas-app\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#46 {main}
"} 
[2025-08-01 13:58:18] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:83)
[stacktrace]
#0 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(380): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(NULL)
#1 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(81): tap(NULL, Object(Closure))
#2 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(931): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(815): Illuminate\\Container\\Container->build(Object(Closure))
#6 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1046): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1028): Illuminate\\Container\\Container->make('encrypter', Array)
#9 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\PassportServiceProvider.php(357): Illuminate\\Foundation\\Application->make('encrypter')
#10 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\PassportServiceProvider.php(337): Laravel\\Passport\\PassportServiceProvider->makeGuard(Array)
#11 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(113): Laravel\\Passport\\PassportServiceProvider->Laravel\\Passport\\{closure}(Object(Illuminate\\Foundation\\Application), 'api', Array)
#12 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(90): Illuminate\\Auth\\AuthManager->callCustomCreator('api', Array)
#13 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(70): Illuminate\\Auth\\AuthManager->resolve('api')
#14 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(83): Illuminate\\Auth\\AuthManager->guard('api')
#15 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(62): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#16 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#17 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1188): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#44 C:\\xampp\\htdocs\\tvnas-app\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\tvnas-app\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#46 {main}
"} 
[2025-08-01 13:59:51] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:83)
[stacktrace]
#0 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(380): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(NULL)
#1 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(81): tap(NULL, Object(Closure))
#2 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(931): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(815): Illuminate\\Container\\Container->build(Object(Closure))
#6 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1046): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1028): Illuminate\\Container\\Container->make('encrypter', Array)
#9 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\PassportServiceProvider.php(357): Illuminate\\Foundation\\Application->make('encrypter')
#10 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\PassportServiceProvider.php(337): Laravel\\Passport\\PassportServiceProvider->makeGuard(Array)
#11 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(113): Laravel\\Passport\\PassportServiceProvider->Laravel\\Passport\\{closure}(Object(Illuminate\\Foundation\\Application), 'api', Array)
#12 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(90): Illuminate\\Auth\\AuthManager->callCustomCreator('api', Array)
#13 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(70): Illuminate\\Auth\\AuthManager->resolve('api')
#14 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(83): Illuminate\\Auth\\AuthManager->guard('api')
#15 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(62): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#16 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#17 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1188): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#44 C:\\xampp\\htdocs\\tvnas-app\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\tvnas-app\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#46 {main}
"} 
[2025-08-01 14:00:23] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:83)
[stacktrace]
#0 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(380): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(NULL)
#1 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(81): tap(NULL, Object(Closure))
#2 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(931): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(815): Illuminate\\Container\\Container->build(Object(Closure))
#6 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1046): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1028): Illuminate\\Container\\Container->make('encrypter', Array)
#9 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\PassportServiceProvider.php(357): Illuminate\\Foundation\\Application->make('encrypter')
#10 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\PassportServiceProvider.php(337): Laravel\\Passport\\PassportServiceProvider->makeGuard(Array)
#11 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(113): Laravel\\Passport\\PassportServiceProvider->Laravel\\Passport\\{closure}(Object(Illuminate\\Foundation\\Application), 'api', Array)
#12 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(90): Illuminate\\Auth\\AuthManager->callCustomCreator('api', Array)
#13 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(70): Illuminate\\Auth\\AuthManager->resolve('api')
#14 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(83): Illuminate\\Auth\\AuthManager->guard('api')
#15 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(62): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#16 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#17 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1188): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#44 C:\\xampp\\htdocs\\tvnas-app\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\tvnas-app\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#46 {main}
"} 
[2025-08-02 08:17:38] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:83)
[stacktrace]
#0 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(380): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(NULL)
#1 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(81): tap(NULL, Object(Closure))
#2 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(931): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(815): Illuminate\\Container\\Container->build(Object(Closure))
#6 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1046): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1028): Illuminate\\Container\\Container->make('encrypter', Array)
#9 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\PassportServiceProvider.php(357): Illuminate\\Foundation\\Application->make('encrypter')
#10 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\PassportServiceProvider.php(337): Laravel\\Passport\\PassportServiceProvider->makeGuard(Array)
#11 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(113): Laravel\\Passport\\PassportServiceProvider->Laravel\\Passport\\{closure}(Object(Illuminate\\Foundation\\Application), 'api', Array)
#12 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(90): Illuminate\\Auth\\AuthManager->callCustomCreator('api', Array)
#13 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(70): Illuminate\\Auth\\AuthManager->resolve('api')
#14 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(83): Illuminate\\Auth\\AuthManager->guard('api')
#15 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(62): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#16 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#17 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1188): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#44 C:\\xampp\\htdocs\\tvnas-app\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\tvnas-app\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#46 {main}
"} 
[2025-08-02 08:25:06] local.INFO: Starting executeAction {"job_id":"9f88c3f6-bb46-48d7-a382-8dda2f01715d","stage_id":"9f88c2b3-1275-458e-ab88-71e1f9208d42","action_id":"9f88c2b3-193c-4975-9192-c8b4165e52ac","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb00489","pending_approval_id":"9f88c3f7-0212-45a9-a218-823a57319820","condition_ids":"[]","email_condition_ids":"[]"} 
[2025-08-02 08:25:06] local.INFO: ExecuteAction completed successfully {"job_id":"9f88c3f6-bb46-48d7-a382-8dda2f01715d","stage_id":"9f88c2b3-1275-458e-ab88-71e1f9208d42","action_id":"9f88c2b3-193c-4975-9192-c8b4165e52ac"} 
[2025-08-02 08:26:26] local.INFO: Starting executeAction {"job_id":"9f88c3f6-bb46-48d7-a382-8dda2f01715d","stage_id":"9f88c2b3-1818-4573-a146-f31f4adcc51c","action_id":"9f88c2b3-193c-4975-9192-c8b4165e52ac","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb00370","pending_approval_id":"9f88c643-34dd-4344-9b08-04a7f33d2f7e","condition_ids":"[\"9f88c2b3-2051-48c9-ba85-4389d1966f02\"]","email_condition_ids":"[]"} 
[2025-08-02 08:26:26] local.WARNING: No data processed in actionProcess {"job_id":"9f88c3f6-bb46-48d7-a382-8dda2f01715d","stage_id":"9f88c2b3-1818-4573-a146-f31f4adcc51c","action_id":"9f88c2b3-193c-4975-9192-c8b4165e52ac"} 
[2025-08-02 08:26:26] local.ERROR: ActionProcess returned false {"job_id":"9f88c3f6-bb46-48d7-a382-8dda2f01715d","stage_id":"9f88c2b3-1818-4573-a146-f31f4adcc51c","action_id":"9f88c2b3-193c-4975-9192-c8b4165e52ac"} 
[2025-08-02 08:26:32] local.INFO: Starting executeAction {"job_id":"9f88c3f6-bb46-48d7-a382-8dda2f01715d","stage_id":"9f88c2b3-1818-4573-a146-f31f4adcc51c","action_id":"9f88c2b3-193c-4975-9192-c8b4165e52ac","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb00370","pending_approval_id":"9f88c643-34dd-4344-9b08-04a7f33d2f7e","condition_ids":"[\"9f88c2b3-2cb7-457c-b47d-142179bcfd98\"]","email_condition_ids":"[]"} 
[2025-08-02 08:26:32] local.WARNING: No data processed in actionProcess {"job_id":"9f88c3f6-bb46-48d7-a382-8dda2f01715d","stage_id":"9f88c2b3-1818-4573-a146-f31f4adcc51c","action_id":"9f88c2b3-193c-4975-9192-c8b4165e52ac"} 
[2025-08-02 08:26:32] local.ERROR: ActionProcess returned false {"job_id":"9f88c3f6-bb46-48d7-a382-8dda2f01715d","stage_id":"9f88c2b3-1818-4573-a146-f31f4adcc51c","action_id":"9f88c2b3-193c-4975-9192-c8b4165e52ac"} 
[2025-08-02 08:26:40] local.INFO: Starting executeAction {"job_id":"9f88c3f6-bb46-48d7-a382-8dda2f01715d","stage_id":"9f88c2b3-1818-4573-a146-f31f4adcc51c","action_id":"9f88c2b3-193c-4975-9192-c8b4165e52ac","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb00370","pending_approval_id":"9f88c643-34dd-4344-9b08-04a7f33d2f7e","condition_ids":"[\"9f88c2b3-3694-4b15-9245-7c0e5c41c101\"]","email_condition_ids":"[]"} 
[2025-08-02 08:26:40] local.WARNING: No data processed in actionProcess {"job_id":"9f88c3f6-bb46-48d7-a382-8dda2f01715d","stage_id":"9f88c2b3-1818-4573-a146-f31f4adcc51c","action_id":"9f88c2b3-193c-4975-9192-c8b4165e52ac"} 
[2025-08-02 08:26:40] local.ERROR: ActionProcess returned false {"job_id":"9f88c3f6-bb46-48d7-a382-8dda2f01715d","stage_id":"9f88c2b3-1818-4573-a146-f31f4adcc51c","action_id":"9f88c2b3-193c-4975-9192-c8b4165e52ac"} 
[2025-08-02 08:26:45] local.INFO: Starting executeAction {"job_id":"9f88c3f6-bb46-48d7-a382-8dda2f01715d","stage_id":"9f88c2b3-1818-4573-a146-f31f4adcc51c","action_id":"9f88c2b3-193c-4975-9192-c8b4165e52ac","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb00370","pending_approval_id":"9f88c643-34dd-4344-9b08-04a7f33d2f7e","condition_ids":"[\"9f88c2b3-43ea-4c27-b976-f4abcbde1d47\"]","email_condition_ids":"[]"} 
[2025-08-02 08:26:45] local.INFO: ExecuteAction completed successfully {"job_id":"9f88c3f6-bb46-48d7-a382-8dda2f01715d","stage_id":"9f88c2b3-1818-4573-a146-f31f4adcc51c","action_id":"9f88c2b3-193c-4975-9192-c8b4165e52ac"} 
[2025-08-03 16:13:03] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:83)
[stacktrace]
#0 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(380): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(NULL)
#1 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(81): tap(NULL, Object(Closure))
#2 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(931): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(815): Illuminate\\Container\\Container->build(Object(Closure))
#6 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1046): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1028): Illuminate\\Container\\Container->make('encrypter', Array)
#9 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1112): Illuminate\\Foundation\\Application->make('encrypter')
#10 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1022): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#11 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(973): Illuminate\\Container\\Container->resolveDependencies(Array)
#12 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(815): Illuminate\\Container\\Container->build('Illuminate\\\\Cook...')
#13 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1046): Illuminate\\Container\\Container->resolve('Illuminate\\\\Cook...', Array, true)
#14 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('Illuminate\\\\Cook...', Array)
#15 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1028): Illuminate\\Container\\Container->make('Illuminate\\\\Cook...', Array)
#16 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(172): Illuminate\\Foundation\\Application->make('Illuminate\\\\Cook...')
#17 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1188): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#44 C:\\xampp\\htdocs\\tvnas-app\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\tvnas-app\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#46 {main}
"} 
[2025-08-03 16:13:04] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:83)
[stacktrace]
#0 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(380): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(NULL)
#1 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(81): tap(NULL, Object(Closure))
#2 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(931): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(815): Illuminate\\Container\\Container->build(Object(Closure))
#6 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1046): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1028): Illuminate\\Container\\Container->make('encrypter', Array)
#9 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1112): Illuminate\\Foundation\\Application->make('encrypter')
#10 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1022): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#11 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(973): Illuminate\\Container\\Container->resolveDependencies(Array)
#12 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(815): Illuminate\\Container\\Container->build('Illuminate\\\\Cook...')
#13 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1046): Illuminate\\Container\\Container->resolve('Illuminate\\\\Cook...', Array, true)
#14 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('Illuminate\\\\Cook...', Array)
#15 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1028): Illuminate\\Container\\Container->make('Illuminate\\\\Cook...', Array)
#16 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(258): Illuminate\\Foundation\\Application->make('Illuminate\\\\Cook...')
#17 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(216): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#18 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1190): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#19 C:\\xampp\\htdocs\\tvnas-app\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\tvnas-app\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#21 {main}
"} 
[2025-08-03 16:40:17] local.INFO: SendDynamicEmailJob: Email đã được gửi thành công. {"template_id":"9f88c2b3-13f5-40c9-910f-0fbc0f4aae77","name_title":"Xin phê duyệt thay đổi thông tin budget Infrastructure Management (DA)","to":"<EMAIL>, <EMAIL>"} 
[2025-08-04 08:06:00] local.INFO: SendDynamicEmailJob: Email đã được gửi thành công. {"template_id":"9f88c2b3-13f5-40c9-910f-0fbc0f4aae77","name_title":"Xin phê duyệt thay đổi thông tin budget Infrastructure Management (DA)","to":"<EMAIL>, <EMAIL>"} 
[2025-08-04 08:14:35] local.INFO: SendDynamicEmailJob: Email đã được gửi thành công. {"template_id":"9f88c2b3-13f5-40c9-910f-0fbc0f4aae77","name_title":"Xin phê duyệt thay đổi thông tin budget Công ty TOTO Việt Nam","to":"<EMAIL>, <EMAIL>"} 
[2025-08-04 08:28:45] local.INFO: SendDynamicEmailJob: Email đã được gửi thành công. {"template_id":"9f88c2b3-13f5-40c9-910f-0fbc0f4aae77","name_title":"Xin phê duyệt thay đổi thông tin budget Chi nhánh Đông Anh","to":"<EMAIL>, <EMAIL>"} 
[2025-08-04 08:30:18] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:83)
[stacktrace]
#0 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(380): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(NULL)
#1 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(81): tap(NULL, Object(Closure))
#2 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(931): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(815): Illuminate\\Container\\Container->build(Object(Closure))
#6 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1046): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1028): Illuminate\\Container\\Container->make('encrypter', Array)
#9 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\PassportServiceProvider.php(357): Illuminate\\Foundation\\Application->make('encrypter')
#10 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\PassportServiceProvider.php(337): Laravel\\Passport\\PassportServiceProvider->makeGuard(Array)
#11 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(113): Laravel\\Passport\\PassportServiceProvider->Laravel\\Passport\\{closure}(Object(Illuminate\\Foundation\\Application), 'api', Array)
#12 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(90): Illuminate\\Auth\\AuthManager->callCustomCreator('api', Array)
#13 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(70): Illuminate\\Auth\\AuthManager->resolve('api')
#14 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(83): Illuminate\\Auth\\AuthManager->guard('api')
#15 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(62): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#16 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#17 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1188): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#44 C:\\xampp\\htdocs\\tvnas-app\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\tvnas-app\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#46 {main}
"} 
[2025-08-04 08:31:02] local.INFO: SendDynamicEmailJob: Email đã được gửi thành công. {"template_id":"9f88c2b3-13f5-40c9-910f-0fbc0f4aae77","name_title":"Xin phê duyệt thay đổi thông tin budget Chi nhánh Đông Anh","to":"<EMAIL>, <EMAIL>"} 
[2025-08-04 08:32:41] local.INFO: SendDynamicEmailJob: Email đã được gửi thành công. {"template_id":"9f88c2b3-13f5-40c9-910f-0fbc0f4aae77","name_title":"Xin phê duyệt thay đổi thông tin budget Công ty TOTO Việt Nam","to":"<EMAIL>, <EMAIL>"} 
[2025-08-04 08:48:50] production.ERROR: The resource owner or authorization server denied the request. {"exception":"[object] (League\\OAuth2\\Server\\Exception\\OAuthServerException(code: 9): The resource owner or authorization server denied the request. at C:\\xampp\\htdocs\\tvnas-app\\vendor\\league\\oauth2-server\\src\\Exception\\OAuthServerException.php:243)
[stacktrace]
#0 C:\\xampp\\htdocs\\tvnas-app\\vendor\\league\\oauth2-server\\src\\AuthorizationValidators\\BearerTokenValidator.php(123): League\\OAuth2\\Server\\Exception\\OAuthServerException::accessDenied('Access token ha...')
#1 C:\\xampp\\htdocs\\tvnas-app\\vendor\\league\\oauth2-server\\src\\ResourceServer.php(84): League\\OAuth2\\Server\\AuthorizationValidators\\BearerTokenValidator->validateAuthorization(Object(Nyholm\\Psr7\\ServerRequest))
#2 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(232): League\\OAuth2\\Server\\ResourceServer->validateAuthenticatedRequest(Object(Nyholm\\Psr7\\ServerRequest))
#3 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(178): Laravel\\Passport\\Guards\\TokenGuard->getPsrRequestViaBearerToken(Object(Illuminate\\Http\\Request))
#4 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(120): Laravel\\Passport\\Guards\\TokenGuard->authenticateViaBearerToken(Object(Illuminate\\Http\\Request))
#5 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php(56): Laravel\\Passport\\Guards\\TokenGuard->user()
#6 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(83): Laravel\\Passport\\Guards\\TokenGuard->check()
#7 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(62): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#8 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#9 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#11 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#12 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#13 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#14 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#15 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1188): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#36 C:\\xampp\\htdocs\\tvnas-app\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\tvnas-app\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#38 {main}
"} 
[2025-08-04 08:48:58] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:83)
[stacktrace]
#0 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(380): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(NULL)
#1 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(81): tap(NULL, Object(Closure))
#2 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(931): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(815): Illuminate\\Container\\Container->build(Object(Closure))
#6 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1046): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1028): Illuminate\\Container\\Container->make('encrypter', Array)
#9 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\PassportServiceProvider.php(357): Illuminate\\Foundation\\Application->make('encrypter')
#10 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\PassportServiceProvider.php(337): Laravel\\Passport\\PassportServiceProvider->makeGuard(Array)
#11 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(113): Laravel\\Passport\\PassportServiceProvider->Laravel\\Passport\\{closure}(Object(Illuminate\\Foundation\\Application), 'api', Array)
#12 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(90): Illuminate\\Auth\\AuthManager->callCustomCreator('api', Array)
#13 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(70): Illuminate\\Auth\\AuthManager->resolve('api')
#14 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(83): Illuminate\\Auth\\AuthManager->guard('api')
#15 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(62): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#16 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#17 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1188): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#44 C:\\xampp\\htdocs\\tvnas-app\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\tvnas-app\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#46 {main}
"} 
[2025-08-04 08:49:34] local.INFO: SendDynamicEmailJob: Email đã được gửi thành công. {"template_id":"9f88c2b3-13f5-40c9-910f-0fbc0f4aae77","name_title":"Xin phê duyệt thay đổi thông tin budget Công ty TOTO Việt Nam","to":"<EMAIL>, <EMAIL>"} 
[2025-08-04 09:05:31] local.INFO: SendDynamicEmailJob: Email đã được gửi thành công. {"template_id":"9f88c2b3-13f5-40c9-910f-0fbc0f4aae77","name_title":"Xin phê duyệt thay đổi thông tin budget Công ty TOTO Việt Nam","to":"<EMAIL>, <EMAIL>"} 
[2025-08-04 09:07:59] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:83)
[stacktrace]
#0 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(380): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(NULL)
#1 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(81): tap(NULL, Object(Closure))
#2 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(931): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(815): Illuminate\\Container\\Container->build(Object(Closure))
#6 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1046): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1028): Illuminate\\Container\\Container->make('encrypter', Array)
#9 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\PassportServiceProvider.php(357): Illuminate\\Foundation\\Application->make('encrypter')
#10 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\PassportServiceProvider.php(337): Laravel\\Passport\\PassportServiceProvider->makeGuard(Array)
#11 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(113): Laravel\\Passport\\PassportServiceProvider->Laravel\\Passport\\{closure}(Object(Illuminate\\Foundation\\Application), 'api', Array)
#12 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(90): Illuminate\\Auth\\AuthManager->callCustomCreator('api', Array)
#13 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(70): Illuminate\\Auth\\AuthManager->resolve('api')
#14 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(83): Illuminate\\Auth\\AuthManager->guard('api')
#15 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(62): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#16 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#17 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1188): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#44 C:\\xampp\\htdocs\\tvnas-app\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\tvnas-app\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#46 {main}
"} 
[2025-08-04 09:10:18] local.INFO: SendDynamicEmailJob: Email đã được gửi thành công. {"template_id":"9f88c2b3-13f5-40c9-910f-0fbc0f4aae77","name_title":"Xin phê duyệt thay đổi thông tin budget Chi nhánh Đông Anh","to":"<EMAIL>, <EMAIL>"} 
[2025-08-04 09:13:26] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:83)
[stacktrace]
#0 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(380): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(NULL)
#1 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(81): tap(NULL, Object(Closure))
#2 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(931): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(815): Illuminate\\Container\\Container->build(Object(Closure))
#6 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1046): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1028): Illuminate\\Container\\Container->make('encrypter', Array)
#9 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\PassportServiceProvider.php(357): Illuminate\\Foundation\\Application->make('encrypter')
#10 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\PassportServiceProvider.php(337): Laravel\\Passport\\PassportServiceProvider->makeGuard(Array)
#11 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(113): Laravel\\Passport\\PassportServiceProvider->Laravel\\Passport\\{closure}(Object(Illuminate\\Foundation\\Application), 'api', Array)
#12 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(90): Illuminate\\Auth\\AuthManager->callCustomCreator('api', Array)
#13 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(70): Illuminate\\Auth\\AuthManager->resolve('api')
#14 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(83): Illuminate\\Auth\\AuthManager->guard('api')
#15 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(62): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#16 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#17 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1188): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#44 C:\\xampp\\htdocs\\tvnas-app\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\tvnas-app\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#46 {main}
"} 
[2025-08-04 09:13:27] production.ERROR: The resource owner or authorization server denied the request. {"exception":"[object] (League\\OAuth2\\Server\\Exception\\OAuthServerException(code: 9): The resource owner or authorization server denied the request. at C:\\xampp\\htdocs\\tvnas-app\\vendor\\league\\oauth2-server\\src\\Exception\\OAuthServerException.php:243)
[stacktrace]
#0 C:\\xampp\\htdocs\\tvnas-app\\vendor\\league\\oauth2-server\\src\\AuthorizationValidators\\BearerTokenValidator.php(123): League\\OAuth2\\Server\\Exception\\OAuthServerException::accessDenied('Access token ha...')
#1 C:\\xampp\\htdocs\\tvnas-app\\vendor\\league\\oauth2-server\\src\\ResourceServer.php(84): League\\OAuth2\\Server\\AuthorizationValidators\\BearerTokenValidator->validateAuthorization(Object(Nyholm\\Psr7\\ServerRequest))
#2 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(232): League\\OAuth2\\Server\\ResourceServer->validateAuthenticatedRequest(Object(Nyholm\\Psr7\\ServerRequest))
#3 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(178): Laravel\\Passport\\Guards\\TokenGuard->getPsrRequestViaBearerToken(Object(Illuminate\\Http\\Request))
#4 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(120): Laravel\\Passport\\Guards\\TokenGuard->authenticateViaBearerToken(Object(Illuminate\\Http\\Request))
#5 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php(56): Laravel\\Passport\\Guards\\TokenGuard->user()
#6 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(83): Laravel\\Passport\\Guards\\TokenGuard->check()
#7 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(62): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#8 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#9 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#11 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#12 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#13 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#14 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#15 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1188): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#36 C:\\xampp\\htdocs\\tvnas-app\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\tvnas-app\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#38 {main}
"} 
[2025-08-04 09:13:27] production.ERROR: The resource owner or authorization server denied the request. {"exception":"[object] (League\\OAuth2\\Server\\Exception\\OAuthServerException(code: 9): The resource owner or authorization server denied the request. at C:\\xampp\\htdocs\\tvnas-app\\vendor\\league\\oauth2-server\\src\\Exception\\OAuthServerException.php:243)
[stacktrace]
#0 C:\\xampp\\htdocs\\tvnas-app\\vendor\\league\\oauth2-server\\src\\AuthorizationValidators\\BearerTokenValidator.php(123): League\\OAuth2\\Server\\Exception\\OAuthServerException::accessDenied('Access token ha...')
#1 C:\\xampp\\htdocs\\tvnas-app\\vendor\\league\\oauth2-server\\src\\ResourceServer.php(84): League\\OAuth2\\Server\\AuthorizationValidators\\BearerTokenValidator->validateAuthorization(Object(Nyholm\\Psr7\\ServerRequest))
#2 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(232): League\\OAuth2\\Server\\ResourceServer->validateAuthenticatedRequest(Object(Nyholm\\Psr7\\ServerRequest))
#3 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(178): Laravel\\Passport\\Guards\\TokenGuard->getPsrRequestViaBearerToken(Object(Illuminate\\Http\\Request))
#4 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(120): Laravel\\Passport\\Guards\\TokenGuard->authenticateViaBearerToken(Object(Illuminate\\Http\\Request))
#5 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php(56): Laravel\\Passport\\Guards\\TokenGuard->user()
#6 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(83): Laravel\\Passport\\Guards\\TokenGuard->check()
#7 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(62): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#8 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#9 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#11 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#12 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#13 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#14 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#15 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1188): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#36 C:\\xampp\\htdocs\\tvnas-app\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\tvnas-app\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#38 {main}
"} 
[2025-08-04 09:13:34] production.ERROR: The resource owner or authorization server denied the request. {"exception":"[object] (League\\OAuth2\\Server\\Exception\\OAuthServerException(code: 9): The resource owner or authorization server denied the request. at C:\\xampp\\htdocs\\tvnas-app\\vendor\\league\\oauth2-server\\src\\Exception\\OAuthServerException.php:243)
[stacktrace]
#0 C:\\xampp\\htdocs\\tvnas-app\\vendor\\league\\oauth2-server\\src\\AuthorizationValidators\\BearerTokenValidator.php(123): League\\OAuth2\\Server\\Exception\\OAuthServerException::accessDenied('Access token ha...')
#1 C:\\xampp\\htdocs\\tvnas-app\\vendor\\league\\oauth2-server\\src\\ResourceServer.php(84): League\\OAuth2\\Server\\AuthorizationValidators\\BearerTokenValidator->validateAuthorization(Object(Nyholm\\Psr7\\ServerRequest))
#2 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(232): League\\OAuth2\\Server\\ResourceServer->validateAuthenticatedRequest(Object(Nyholm\\Psr7\\ServerRequest))
#3 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(178): Laravel\\Passport\\Guards\\TokenGuard->getPsrRequestViaBearerToken(Object(Illuminate\\Http\\Request))
#4 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(120): Laravel\\Passport\\Guards\\TokenGuard->authenticateViaBearerToken(Object(Illuminate\\Http\\Request))
#5 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php(56): Laravel\\Passport\\Guards\\TokenGuard->user()
#6 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(83): Laravel\\Passport\\Guards\\TokenGuard->check()
#7 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(62): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#8 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#9 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#11 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#12 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#13 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#14 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#15 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1188): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#36 C:\\xampp\\htdocs\\tvnas-app\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\tvnas-app\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#38 {main}
"} 
[2025-08-04 09:14:11] local.INFO: SendDynamicEmailJob: Email đã được gửi thành công. {"template_id":"9f88c2b3-13f5-40c9-910f-0fbc0f4aae77","name_title":"Xin phê duyệt thay đổi thông tin budget Chi nhánh Đông Anh","to":"<EMAIL>, <EMAIL>"} 
[2025-08-04 09:16:40] local.INFO: SendDynamicEmailJob: Email đã được gửi thành công. {"template_id":"9f88c2b3-13f5-40c9-910f-0fbc0f4aae77","name_title":"Xin phê duyệt thay đổi thông tin budget Business Planning","to":"<EMAIL>, <EMAIL>"} 
[2025-08-04 09:50:03] local.INFO: SendDynamicEmailJob: Email đã được gửi thành công. {"template_id":"9f88c2b3-13f5-40c9-910f-0fbc0f4aae77","name_title":"Xin phê duyệt thay đổi thông tin budget Công ty TOTO Việt Nam","to":"<EMAIL>, <EMAIL>"} 
[2025-08-04 09:51:47] local.INFO: SendDynamicEmailJob: Email đã được gửi thành công. {"template_id":"9f88c2b3-13f5-40c9-910f-0fbc0f4aae77","name_title":"Xin phê duyệt thay đổi thông tin budget Công ty TOTO Việt Nam","to":"<EMAIL>, <EMAIL>"} 
[2025-08-04 09:56:39] local.INFO: SendDynamicEmailJob: Email đã được gửi thành công. {"template_id":"9f88c2b3-13f5-40c9-910f-0fbc0f4aae77","name_title":"Xin phê duyệt thay đổi thông tin budget Infrastructure Management (DA)","to":"<EMAIL>, <EMAIL>"} 
[2025-08-04 09:58:03] local.INFO: SendDynamicEmailJob: Email đã được gửi thành công. {"template_id":"9f88c2b3-13f5-40c9-910f-0fbc0f4aae77","name_title":"Xin phê duyệt thay đổi thông tin budget Công ty TOTO Việt Nam","to":"<EMAIL>, <EMAIL>"} 
[2025-08-04 10:02:53] local.INFO: SendDynamicEmailJob: Email đã được gửi thành công. {"template_id":"9f88c2b3-13f5-40c9-910f-0fbc0f4aae77","name_title":"Xin phê duyệt thay đổi thông tin budget Business Resource Management","to":"<EMAIL>, <EMAIL>"} 
[2025-08-04 10:15:49] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:83)
[stacktrace]
#0 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(380): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(NULL)
#1 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(81): tap(NULL, Object(Closure))
#2 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(931): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(815): Illuminate\\Container\\Container->build(Object(Closure))
#6 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1046): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1028): Illuminate\\Container\\Container->make('encrypter', Array)
#9 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\PassportServiceProvider.php(357): Illuminate\\Foundation\\Application->make('encrypter')
#10 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\PassportServiceProvider.php(337): Laravel\\Passport\\PassportServiceProvider->makeGuard(Array)
#11 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(113): Laravel\\Passport\\PassportServiceProvider->Laravel\\Passport\\{closure}(Object(Illuminate\\Foundation\\Application), 'api', Array)
#12 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(90): Illuminate\\Auth\\AuthManager->callCustomCreator('api', Array)
#13 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(70): Illuminate\\Auth\\AuthManager->resolve('api')
#14 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(83): Illuminate\\Auth\\AuthManager->guard('api')
#15 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(62): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#16 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#17 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1188): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#44 C:\\xampp\\htdocs\\tvnas-app\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\tvnas-app\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#46 {main}
"} 
[2025-08-04 10:17:54] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:83)
[stacktrace]
#0 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(380): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(NULL)
#1 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(81): tap(NULL, Object(Closure))
#2 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(931): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(815): Illuminate\\Container\\Container->build(Object(Closure))
#6 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1046): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1028): Illuminate\\Container\\Container->make('encrypter', Array)
#9 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\PassportServiceProvider.php(357): Illuminate\\Foundation\\Application->make('encrypter')
#10 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\PassportServiceProvider.php(337): Laravel\\Passport\\PassportServiceProvider->makeGuard(Array)
#11 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(113): Laravel\\Passport\\PassportServiceProvider->Laravel\\Passport\\{closure}(Object(Illuminate\\Foundation\\Application), 'api', Array)
#12 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(90): Illuminate\\Auth\\AuthManager->callCustomCreator('api', Array)
#13 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(70): Illuminate\\Auth\\AuthManager->resolve('api')
#14 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(83): Illuminate\\Auth\\AuthManager->guard('api')
#15 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(62): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#16 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#17 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1188): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#44 C:\\xampp\\htdocs\\tvnas-app\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\tvnas-app\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#46 {main}
"} 
[2025-08-04 10:29:35] local.INFO: Starting executeAction {"job_id":"9f8cc86d-4702-4b6a-a2e2-73cd0e3f8a70","stage_id":"9f88c2b3-1275-458e-ab88-71e1f9208d42","action_id":"9f88c2b3-193c-4975-9192-c8b4165e52ac","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb02807","pending_approval_id":"9f8cc86d-7c78-4c23-a0b4-2ccfd1043d4a","condition_ids":"[]","email_condition_ids":"[]"} 
[2025-08-04 10:29:35] local.INFO: ExecuteAction completed successfully {"job_id":"9f8cc86d-4702-4b6a-a2e2-73cd0e3f8a70","stage_id":"9f88c2b3-1275-458e-ab88-71e1f9208d42","action_id":"9f88c2b3-193c-4975-9192-c8b4165e52ac"} 
[2025-08-04 10:40:56] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:83)
[stacktrace]
#0 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(380): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(NULL)
#1 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(81): tap(NULL, Object(Closure))
#2 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(931): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(815): Illuminate\\Container\\Container->build(Object(Closure))
#6 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1046): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1028): Illuminate\\Container\\Container->make('encrypter', Array)
#9 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\PassportServiceProvider.php(357): Illuminate\\Foundation\\Application->make('encrypter')
#10 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\PassportServiceProvider.php(337): Laravel\\Passport\\PassportServiceProvider->makeGuard(Array)
#11 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(113): Laravel\\Passport\\PassportServiceProvider->Laravel\\Passport\\{closure}(Object(Illuminate\\Foundation\\Application), 'api', Array)
#12 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(90): Illuminate\\Auth\\AuthManager->callCustomCreator('api', Array)
#13 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(70): Illuminate\\Auth\\AuthManager->resolve('api')
#14 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(83): Illuminate\\Auth\\AuthManager->guard('api')
#15 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(62): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#16 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#17 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1188): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#44 C:\\xampp\\htdocs\\tvnas-app\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\tvnas-app\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#46 {main}
"} 
[2025-08-04 10:46:38] local.ERROR: Call to a member function withoutGlobalScopes() on null {"userId":"9f793fc2-3089-4dbd-9215-fdd68fb02807","exception":"[object] (Error(code: 0): Call to a member function withoutGlobalScopes() on null at C:\\xampp\\htdocs\\tvnas-app\\app\\Http\\Controllers\\Job\\SaveJobController.php:295)
[stacktrace]
#0 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Job\\SaveJobController->executeAction(Object(Illuminate\\Http\\Request))
#1 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(264): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Job\\SaveJobController), 'executeAction')
#2 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(210): Illuminate\\Routing\\Route->runController()
#3 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#4 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#5 C:\\xampp\\htdocs\\tvnas-app\\app\\Http\\Middleware\\IdentifyTenant.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#6 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\IdentifyTenant->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#7 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authorize.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authorize->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'VIEW-JOB')
#9 C:\\xampp\\htdocs\\tvnas-app\\app\\Http\\Middleware\\SetLocale.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SetLocale->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(64): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#15 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#17 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#18 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#19 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#21 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#40 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1188): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#42 C:\\xampp\\htdocs\\tvnas-app\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\tvnas-app\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#44 {main}
"} 
[2025-08-04 11:13:08] local.ERROR: Undefined property: App\Models\User::$roles {"userId":"9f793fc2-3089-4dbd-9215-fdd68fb13494","exception":"[object] (ErrorException(code: 0): Undefined property: App\\Models\\User::$roles at C:\\xampp\\htdocs\\tvnas-app\\app\\Models\\User.php:182)
[stacktrace]
#0 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Undefined prope...', 'C:\\\\xampp\\\\htdocs...', 182)
#1 C:\\xampp\\htdocs\\tvnas-app\\app\\Models\\User.php(182): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Undefined prope...', 'C:\\\\xampp\\\\htdocs...', 182)
#2 C:\\xampp\\htdocs\\tvnas-app\\app\\Models\\Scopes\\TenantScope.php(13): App\\Models\\User->hasRole('Qu\\xE1\\xBA\\xA3n tr\\xE1\\xBB\\x8B vi...')
#3 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1423): App\\Models\\Scopes\\TenantScope->apply(Object(Illuminate\\Database\\Eloquent\\Builder), Object(App\\Models\\Role))
#4 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1450): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(Illuminate\\Database\\Eloquent\\Builder))
#5 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1411): Illuminate\\Database\\Eloquent\\Builder->callScope(Object(Closure))
#6 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\BelongsToMany.php(856): Illuminate\\Database\\Eloquent\\Builder->applyScopes()
#7 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\BelongsToMany.php(846): Illuminate\\Database\\Eloquent\\Relations\\BelongsToMany->get()
#8 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasAttributes.php(621): Illuminate\\Database\\Eloquent\\Relations\\BelongsToMany->getResults()
#9 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasAttributes.php(559): Illuminate\\Database\\Eloquent\\Model->getRelationshipFromMethod('roles')
#10 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasAttributes.php(484): Illuminate\\Database\\Eloquent\\Model->getRelationValue('roles')
#11 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2259): Illuminate\\Database\\Eloquent\\Model->getAttribute('roles')
#12 C:\\xampp\\htdocs\\tvnas-app\\app\\Models\\User.php(182): Illuminate\\Database\\Eloquent\\Model->__get('roles')
#13 C:\\xampp\\htdocs\\tvnas-app\\app\\Http\\Controllers\\Api\\Auth\\AuthController.php(72): App\\Models\\User->hasRole('Qu\\xE1\\xBA\\xA3n tr\\xE1\\xBB\\x8B vi...')
#14 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Api\\Auth\\AuthController->login(Object(Illuminate\\Http\\Request))
#15 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(264): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Api\\Auth\\AuthController), 'login')
#16 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(210): Illuminate\\Routing\\Route->runController()
#17 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#18 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\xampp\\htdocs\\tvnas-app\\app\\Http\\Middleware\\SetLocale.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SetLocale->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#25 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#26 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#27 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1188): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 C:\\xampp\\htdocs\\tvnas-app\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\tvnas-app\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#52 {main}
"} 
[2025-08-04 11:14:36] local.ERROR: Undefined property: App\Models\User::$roles {"userId":"9f793fc2-3089-4dbd-9215-fdd68fb13494","exception":"[object] (ErrorException(code: 0): Undefined property: App\\Models\\User::$roles at C:\\xampp\\htdocs\\tvnas-app\\app\\Models\\User.php:182)
[stacktrace]
#0 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Undefined prope...', 'C:\\\\xampp\\\\htdocs...', 182)
#1 C:\\xampp\\htdocs\\tvnas-app\\app\\Models\\User.php(182): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Undefined prope...', 'C:\\\\xampp\\\\htdocs...', 182)
#2 C:\\xampp\\htdocs\\tvnas-app\\app\\Models\\Scopes\\TenantScope.php(13): App\\Models\\User->hasRole('Qu\\xE1\\xBA\\xA3n tr\\xE1\\xBB\\x8B vi...')
#3 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1423): App\\Models\\Scopes\\TenantScope->apply(Object(Illuminate\\Database\\Eloquent\\Builder), Object(App\\Models\\Role))
#4 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1450): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(Illuminate\\Database\\Eloquent\\Builder))
#5 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1411): Illuminate\\Database\\Eloquent\\Builder->callScope(Object(Closure))
#6 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\BelongsToMany.php(856): Illuminate\\Database\\Eloquent\\Builder->applyScopes()
#7 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\BelongsToMany.php(846): Illuminate\\Database\\Eloquent\\Relations\\BelongsToMany->get()
#8 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasAttributes.php(621): Illuminate\\Database\\Eloquent\\Relations\\BelongsToMany->getResults()
#9 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasAttributes.php(559): Illuminate\\Database\\Eloquent\\Model->getRelationshipFromMethod('roles')
#10 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasAttributes.php(484): Illuminate\\Database\\Eloquent\\Model->getRelationValue('roles')
#11 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2259): Illuminate\\Database\\Eloquent\\Model->getAttribute('roles')
#12 C:\\xampp\\htdocs\\tvnas-app\\app\\Models\\User.php(182): Illuminate\\Database\\Eloquent\\Model->__get('roles')
#13 C:\\xampp\\htdocs\\tvnas-app\\app\\Http\\Controllers\\Api\\Auth\\AuthController.php(72): App\\Models\\User->hasRole('Qu\\xE1\\xBA\\xA3n tr\\xE1\\xBB\\x8B vi...')
#14 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Api\\Auth\\AuthController->login(Object(Illuminate\\Http\\Request))
#15 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(264): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Api\\Auth\\AuthController), 'login')
#16 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(210): Illuminate\\Routing\\Route->runController()
#17 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#18 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\xampp\\htdocs\\tvnas-app\\app\\Http\\Middleware\\SetLocale.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SetLocale->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#25 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#26 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#27 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1188): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 C:\\xampp\\htdocs\\tvnas-app\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\tvnas-app\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#52 {main}
"} 
[2025-08-04 11:31:42] local.INFO: Starting executeAction {"job_id":"9f8cef2b-1794-489f-a276-af34ed4c2774","stage_id":"9f88c2b3-1275-458e-ab88-71e1f9208d42","action_id":"9f88c2b3-193c-4975-9192-c8b4165e52ac","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb02807","pending_approval_id":"9f8cef2b-4aae-436f-93c1-eac0b9bf3b7f","condition_ids":"[]","email_condition_ids":"[]"} 
[2025-08-04 11:31:43] local.INFO: ExecuteAction completed successfully {"job_id":"9f8cef2b-1794-489f-a276-af34ed4c2774","stage_id":"9f88c2b3-1275-458e-ab88-71e1f9208d42","action_id":"9f88c2b3-193c-4975-9192-c8b4165e52ac"} 
[2025-08-04 11:32:50] local.INFO: Starting executeActionBackTo {"job_id":"9f8ced6e-7b89-41dc-bbd5-2f82bc6334a6","back_to_stage_id":"start","action_back_to_id":"back_to","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb02807","pending_approval_id":"9f8ced6e-9d23-40e8-b426-c3aa37fdf745","email_condition_ids_back_to":[]} 
[2025-08-04 11:32:50] local.INFO: Updated pending approval history {"job_id":"9f8ced6e-7b89-41dc-bbd5-2f82bc6334a6","pending_approval_id":"9f8ced6e-9d23-40e8-b426-c3aa37fdf745","action_back_to_id":"back_to"} 
[2025-08-04 11:32:51] local.INFO: Email sent successfully for back to action {"job_id":"9f8ced6e-7b89-41dc-bbd5-2f82bc6334a6","back_to_stage_id":"start","email_template_ids":["9f88c2b3-1d42-46c2-8aac-d3395ffce103"]} 
[2025-08-04 11:32:51] local.INFO: ExecuteActionBackTo completed successfully {"job_id":"9f8ced6e-7b89-41dc-bbd5-2f82bc6334a6","back_to_stage_id":"start","action_back_to_id":"back_to"} 
[2025-08-04 11:33:21] local.INFO: Starting executeAction {"job_id":"9f8cecf0-d0cb-4138-a82f-01185ad7c21c","stage_id":"9f88c2b3-1275-458e-ab88-71e1f9208d42","action_id":"9f88c2b3-193c-4975-9192-c8b4165e52ac","user_id":"9f793fc2-3089-4dbd-9215-fdd68fb02807","pending_approval_id":"9f8cecf0-ebb1-4f9e-9aff-cbb37bc16e0a","condition_ids":"[]","email_condition_ids":"[]"} 
[2025-08-04 11:33:21] local.INFO: ExecuteAction completed successfully {"job_id":"9f8cecf0-d0cb-4138-a82f-01185ad7c21c","stage_id":"9f88c2b3-1275-458e-ab88-71e1f9208d42","action_id":"9f88c2b3-193c-4975-9192-c8b4165e52ac"} 
[2025-08-04 11:38:59] local.INFO: SendDynamicEmailJob: Email đã được gửi thành công. {"template_id":"9f88c2b3-13f5-40c9-910f-0fbc0f4aae77","name_title":"Xin phê duyệt thay đổi thông tin budget Công ty TOTO Việt Nam","to":"<EMAIL>"} 
[2025-08-04 11:39:01] local.INFO: SendDynamicEmailJob: Email đã được gửi thành công. {"template_id":"9f88c2b3-13f5-40c9-910f-0fbc0f4aae77","name_title":"Xin phê duyệt thay đổi thông tin budget Business Resource Management","to":"<EMAIL>"} 
[2025-08-04 11:39:03] local.INFO: SendDynamicEmailJob: Email đã được gửi thành công. {"template_id":"9f88c2b3-13f5-40c9-910f-0fbc0f4aae77","name_title":"Xin phê duyệt thay đổi thông tin budget Infrastructure Management (DA)","to":"<EMAIL>"} 
[2025-08-04 11:40:55] local.INFO: SendDynamicEmailJob: Email đã được gửi thành công. {"template_id":"9f8d0738-3f87-4118-87a1-fc38014a334c","name_title":"Xin phê duyệt thay đổi thông tin budget Chi nhánh Đông Anh","to":"<EMAIL>, <EMAIL>"} 
[2025-08-04 11:43:47] local.INFO: SendDynamicEmailJob: Email đã được gửi thành công. {"template_id":"9f8d0738-3f87-4118-87a1-fc38014a334c","name_title":"Xin phê duyệt thay đổi thông tin budget Chi nhánh Hưng Yên","to":"<EMAIL>, <EMAIL>"} 
[2025-08-04 11:46:19] local.INFO: SendDynamicEmailJob: Email đã được gửi thành công. {"template_id":"9f8d0738-3f87-4118-87a1-fc38014a334c","name_title":"Xin phê duyệt thay đổi thông tin budget Công ty TOTO Việt Nam","to":"<EMAIL>, <EMAIL>"} 
[2025-08-04 11:47:48] local.INFO: SendDynamicEmailJob: Email đã được gửi thành công. {"template_id":"9f8d0738-3f87-4118-87a1-fc38014a334c","name_title":"Xin phê duyệt thay đổi thông tin budget Công ty TOTO Việt Nam","to":"<EMAIL>, <EMAIL>"} 
[2025-08-04 11:50:37] local.INFO: SendDynamicEmailJob: Email đã được gửi thành công. {"template_id":"9f8d0738-3f87-4118-87a1-fc38014a334c","name_title":"Xin phê duyệt thay đổi thông tin budget Công ty TOTO Việt Nam","to":"<EMAIL>, <EMAIL>"} 
[2025-08-04 14:44:12] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:83)
[stacktrace]
#0 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(380): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(NULL)
#1 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(81): tap(NULL, Object(Closure))
#2 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(931): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(815): Illuminate\\Container\\Container->build(Object(Closure))
#6 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1046): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1028): Illuminate\\Container\\Container->make('encrypter', Array)
#9 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\PassportServiceProvider.php(357): Illuminate\\Foundation\\Application->make('encrypter')
#10 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\PassportServiceProvider.php(337): Laravel\\Passport\\PassportServiceProvider->makeGuard(Array)
#11 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(113): Laravel\\Passport\\PassportServiceProvider->Laravel\\Passport\\{closure}(Object(Illuminate\\Foundation\\Application), 'api', Array)
#12 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(90): Illuminate\\Auth\\AuthManager->callCustomCreator('api', Array)
#13 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(70): Illuminate\\Auth\\AuthManager->resolve('api')
#14 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(83): Illuminate\\Auth\\AuthManager->guard('api')
#15 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(62): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#16 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#17 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1188): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#44 C:\\xampp\\htdocs\\tvnas-app\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\tvnas-app\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#46 {main}
"} 
[2025-08-04 14:50:01] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:83)
[stacktrace]
#0 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(380): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(NULL)
#1 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(81): tap(NULL, Object(Closure))
#2 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(931): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(815): Illuminate\\Container\\Container->build(Object(Closure))
#6 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1046): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1028): Illuminate\\Container\\Container->make('encrypter', Array)
#9 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\PassportServiceProvider.php(357): Illuminate\\Foundation\\Application->make('encrypter')
#10 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\PassportServiceProvider.php(337): Laravel\\Passport\\PassportServiceProvider->makeGuard(Array)
#11 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(113): Laravel\\Passport\\PassportServiceProvider->Laravel\\Passport\\{closure}(Object(Illuminate\\Foundation\\Application), 'api', Array)
#12 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(90): Illuminate\\Auth\\AuthManager->callCustomCreator('api', Array)
#13 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(70): Illuminate\\Auth\\AuthManager->resolve('api')
#14 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(83): Illuminate\\Auth\\AuthManager->guard('api')
#15 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(62): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#16 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#17 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1188): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#44 C:\\xampp\\htdocs\\tvnas-app\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\tvnas-app\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#46 {main}
"} 
[2025-08-04 15:26:55] local.ERROR: Email processing failed {"job_id":"9f8d6314-c4ac-4acb-821a-db7e1f51e6ca","error":"Property [full_name] does not exist on this collection instance."} 
[2025-08-04 15:45:23] local.ERROR: Email processing failed {"job_id":"9f8d69a8-e8a7-4db0-a19a-83c40a7151b6","error":"Property [full_name] does not exist on this collection instance."} 
[2025-08-04 16:10:28] local.ERROR: Database file at path [C:\xampp\htdocs\tvnas-app\database\database.sqlite] does not exist. Ensure this is an absolute path to the database. (Connection: sqlite, SQL: select * from "oauth_access_tokens" where "id" = 8a849ae13df3ac42260e4550c08ea17aa888330168ad1f4c7d4ebb95674ebce10b8b46e92d03d005 limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): Database file at path [C:\\xampp\\htdocs\\tvnas-app\\database\\database.sqlite] does not exist. Ensure this is an absolute path to the database. (Connection: sqlite, SQL: select * from \"oauth_access_tokens\" where \"id\" = 8a849ae13df3ac42260e4550c08ea17aa888330168ad1f4c7d4ebb95674ebce10b8b46e92d03d005 limit 1) at C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select * from \"...', Array, Object(Closure))
#1 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select * from \"...', Array, Object(Closure))
#2 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3132): Illuminate\\Database\\Connection->select('select * from \"...', Array, true)
#3 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3117): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3705): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3116): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(759): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(741): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(344): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\TokenRepository.php(28): Illuminate\\Database\\Eloquent\\Builder->first()
#10 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\TokenRepository.php(100): Laravel\\Passport\\TokenRepository->find('8a849ae13df3ac4...')
#11 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\Bridge\\AccessTokenRepository.php(92): Laravel\\Passport\\TokenRepository->isAccessTokenRevoked('8a849ae13df3ac4...')
#12 C:\\xampp\\htdocs\\tvnas-app\\vendor\\league\\oauth2-server\\src\\AuthorizationValidators\\BearerTokenValidator.php(122): Laravel\\Passport\\Bridge\\AccessTokenRepository->isAccessTokenRevoked('8a849ae13df3ac4...')
#13 C:\\xampp\\htdocs\\tvnas-app\\vendor\\league\\oauth2-server\\src\\ResourceServer.php(84): League\\OAuth2\\Server\\AuthorizationValidators\\BearerTokenValidator->validateAuthorization(Object(Nyholm\\Psr7\\ServerRequest))
#14 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(232): League\\OAuth2\\Server\\ResourceServer->validateAuthenticatedRequest(Object(Nyholm\\Psr7\\ServerRequest))
#15 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(178): Laravel\\Passport\\Guards\\TokenGuard->getPsrRequestViaBearerToken(Object(Illuminate\\Http\\Request))
#16 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(120): Laravel\\Passport\\Guards\\TokenGuard->authenticateViaBearerToken(Object(Illuminate\\Http\\Request))
#17 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php(56): Laravel\\Passport\\Guards\\TokenGuard->user()
#18 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(83): Laravel\\Passport\\Guards\\TokenGuard->check()
#19 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(62): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#20 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#21 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#46 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#47 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1188): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#48 C:\\xampp\\htdocs\\tvnas-app\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#49 C:\\xampp\\htdocs\\tvnas-app\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#50 {main}

[previous exception] [object] (Illuminate\\Database\\SQLiteDatabaseDoesNotExistException(code: 0): Database file at path [C:\\xampp\\htdocs\\tvnas-app\\database\\database.sqlite] does not exist. Ensure this is an absolute path to the database. at C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\SQLiteConnector.php:34)
[stacktrace]
#0 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(224): Illuminate\\Database\\Connectors\\SQLiteConnector->connect(Array)
#1 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#2 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func(Object(Closure))
#3 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1267): Illuminate\\Database\\Connection->getPdo()
#4 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(512): Illuminate\\Database\\Connection->getReadPdo()
#5 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(407): Illuminate\\Database\\Connection->getPdoForSelect(true)
#6 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from \"...', Array)
#7 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select * from \"...', Array, Object(Closure))
#8 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select * from \"...', Array, Object(Closure))
#9 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3132): Illuminate\\Database\\Connection->select('select * from \"...', Array, true)
#10 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3117): Illuminate\\Database\\Query\\Builder->runSelect()
#11 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3705): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#12 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3116): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#13 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(759): Illuminate\\Database\\Query\\Builder->get(Array)
#14 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(741): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#15 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(344): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#16 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\TokenRepository.php(28): Illuminate\\Database\\Eloquent\\Builder->first()
#17 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\TokenRepository.php(100): Laravel\\Passport\\TokenRepository->find('8a849ae13df3ac4...')
#18 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\Bridge\\AccessTokenRepository.php(92): Laravel\\Passport\\TokenRepository->isAccessTokenRevoked('8a849ae13df3ac4...')
#19 C:\\xampp\\htdocs\\tvnas-app\\vendor\\league\\oauth2-server\\src\\AuthorizationValidators\\BearerTokenValidator.php(122): Laravel\\Passport\\Bridge\\AccessTokenRepository->isAccessTokenRevoked('8a849ae13df3ac4...')
#20 C:\\xampp\\htdocs\\tvnas-app\\vendor\\league\\oauth2-server\\src\\ResourceServer.php(84): League\\OAuth2\\Server\\AuthorizationValidators\\BearerTokenValidator->validateAuthorization(Object(Nyholm\\Psr7\\ServerRequest))
#21 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(232): League\\OAuth2\\Server\\ResourceServer->validateAuthenticatedRequest(Object(Nyholm\\Psr7\\ServerRequest))
#22 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(178): Laravel\\Passport\\Guards\\TokenGuard->getPsrRequestViaBearerToken(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(120): Laravel\\Passport\\Guards\\TokenGuard->authenticateViaBearerToken(Object(Illuminate\\Http\\Request))
#24 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php(56): Laravel\\Passport\\Guards\\TokenGuard->user()
#25 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(83): Laravel\\Passport\\Guards\\TokenGuard->check()
#26 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(62): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#27 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#28 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#30 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#32 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#34 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#53 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\tvnas-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1188): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#55 C:\\xampp\\htdocs\\tvnas-app\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#56 C:\\xampp\\htdocs\\tvnas-app\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#57 {main}
"} 
