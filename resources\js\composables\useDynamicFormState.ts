import { reactive, computed } from 'vue'
import useDynamicFormLogic from '@/composables/useDynamicFormLogic'
import { numberCommas } from '@/utils/utils'

export default function useDynamicFormState() {
	const { 
		converFormFields, 
		initializeItemChildrens,
		calculateFormulaWithFormData,
		sumColumn
	} = useDynamicFormLogic()

	// Core dynamic form state
	const createDynamicFormState = () => reactive({
		formData: {} as any,
		itemChildrens: {} as { [key: string]: any[] },
		dataFormFields: [] as Array<any>,
		selectOptionDepartments: [] as Array<any>,
		subColumnTableDescription: {} as { [key: string]: any },
		subColumnTableOptionSelected: {} as { [key: string]: any },
		subColumnTableDescriptionChildren: {} as { [key: string]: { [index: number]: any } },
		subColumnTableOptionSelectedChildren: {} as { [key: string]: { [index: number]: any } },
		maxFiles: 50,
		maxFileSize: '5MB',
		acceptedFileTypes: [
			'image/*',
			'application/pdf', 
			'application/msword', 
			'application/vnd.ms-excel', 
			'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
			'text/plain' 
		]
	})

	// Sorted form fields computed
	const useSortedFormFields = (state: any) => {
		return computed(() => {
			const transformedFields = converFormFields(state.dataFormFields, false);
			initializeItemChildrens(transformedFields, state.itemChildrens);
			return transformedFields;
		});
	}

	// Formula calculations
	const useFormulaCalculations = (sortedFormFields: any, formData: any, itemChildrens: any) => {
		const formulaResults = computed(() => {
			const results = {};
			sortedFormFields.value.forEach((field: any) => {
				if (field.type === 'FORMULA') {
					console.log('Processing FORMULA field:', field.name, 'with value:', field.value);
					const formula = field.value;
					if (formula && formula.startsWith('=')) {
						console.log('Calculating formula:', formula);
						results[field.name] = calculateFormulaWithFormData(field.value, results, formData);
						console.log('Formula result for', field.name, ':', results[field.name]);
					} else if (formula) {
						console.log('Calculating SUMPLUS:', formula);
						results[field.name] = sumColumn(field, itemChildrens);
						console.log('SUMPLUS result for', field.name, ':', results[field.name]);
					} else {
						console.log('No formula found for field:', field.name);
						results[field.name] = 0;
					}
				}
			});
			console.log('All formula results:', results);
			return results;
		});

		const formattedFormulaResults = computed(() => {
			const results = {};
			for (const key in formulaResults.value) {
				const formattedValue = numberCommas(formulaResults.value[key]);
				results[key] = formattedValue;
				// Update formData outside of computed to avoid reactivity issues
				if (formData[key] !== formattedValue) {
					formData[key] = formattedValue;
				}
			}
			console.log('Formatted formula results:', results);
			return results;
		});

		return { formulaResults, formattedFormulaResults };
	}

	return {
		createDynamicFormState,
		useSortedFormFields,
		useFormulaCalculations
	}
}
